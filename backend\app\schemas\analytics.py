"""
Analytics and reporting schemas
"""

from datetime import datetime
from typing import List, Dict, Any, Optional
from decimal import Decimal

from pydantic import BaseModel, Field


class DashboardSummary(BaseModel):
    """Dashboard summary schema"""
    # Transaction metrics
    total_transactions: int = Field(..., description="Total transactions processed")
    total_volume: Decimal = Field(..., description="Total transaction volume")
    fraud_transactions: int = Field(..., description="Number of fraudulent transactions")
    fraud_rate: float = Field(..., description="Fraud rate percentage")
    
    # Processing metrics
    avg_processing_time_ms: float = Field(..., description="Average processing time in milliseconds")
    transactions_per_second: float = Field(..., description="Transactions processed per second")
    
    # Alert metrics
    active_alerts: int = Field(..., description="Number of active alerts")
    high_risk_alerts: int = Field(..., description="Number of high-risk alerts")
    
    # System health
    system_health: str = Field(..., description="Overall system health status")
    ml_model_accuracy: float = Field(..., description="ML model accuracy percentage")
    
    # Time period
    period_start: datetime = Field(..., description="Analysis period start")
    period_end: datetime = Field(..., description="Analysis period end")


class FraudTrends(BaseModel):
    """Fraud trends analysis schema"""
    period_start: datetime = Field(..., description="Analysis period start")
    period_end: datetime = Field(..., description="Analysis period end")
    granularity: str = Field(..., description="Data granularity (hourly, daily, weekly)")
    
    # Trend data points
    data_points: List[Dict[str, Any]] = Field(..., description="Trend data points")
    
    # Summary statistics
    total_fraud_cases: int = Field(..., description="Total fraud cases in period")
    avg_fraud_score: float = Field(..., description="Average fraud score")
    fraud_rate_trend: str = Field(..., description="Fraud rate trend (increasing, decreasing, stable)")
    
    # Top fraud reasons
    top_fraud_reasons: List[Dict[str, Any]] = Field(..., description="Most common fraud reasons")


class TransactionVolume(BaseModel):
    """Transaction volume analysis schema"""
    period_start: datetime = Field(..., description="Analysis period start")
    period_end: datetime = Field(..., description="Analysis period end")
    granularity: str = Field(..., description="Data granularity")
    
    # Volume data
    volume_data: List[Dict[str, Any]] = Field(..., description="Volume data points")
    
    # Peak analysis
    peak_hour: Optional[int] = Field(None, description="Peak transaction hour")
    peak_day: Optional[str] = Field(None, description="Peak transaction day")
    
    # Volume statistics
    total_volume: Decimal = Field(..., description="Total transaction volume")
    avg_transaction_size: Decimal = Field(..., description="Average transaction size")
    
    # Breakdown by type
    volume_by_type: Dict[str, Decimal] = Field(..., description="Volume breakdown by transaction type")


class RiskDistribution(BaseModel):
    """Risk score distribution schema"""
    period_start: datetime = Field(..., description="Analysis period start")
    period_end: datetime = Field(..., description="Analysis period end")
    
    # Distribution buckets
    risk_buckets: List[Dict[str, Any]] = Field(..., description="Risk score distribution buckets")
    
    # Statistics
    avg_risk_score: float = Field(..., description="Average risk score")
    median_risk_score: float = Field(..., description="Median risk score")
    high_risk_percentage: float = Field(..., description="Percentage of high-risk transactions")
    
    # Risk by transaction type
    risk_by_type: Dict[str, float] = Field(..., description="Average risk by transaction type")


class PerformanceMetrics(BaseModel):
    """System performance metrics schema"""
    period_start: datetime = Field(..., description="Analysis period start")
    period_end: datetime = Field(..., description="Analysis period end")
    
    # Processing performance
    avg_processing_time_ms: float = Field(..., description="Average processing time")
    p95_processing_time_ms: float = Field(..., description="95th percentile processing time")
    p99_processing_time_ms: float = Field(..., description="99th percentile processing time")
    
    # Throughput
    transactions_per_second: float = Field(..., description="Average transactions per second")
    peak_tps: float = Field(..., description="Peak transactions per second")
    
    # Accuracy metrics
    model_accuracy: float = Field(..., description="Model accuracy percentage")
    precision: float = Field(..., description="Model precision")
    recall: float = Field(..., description="Model recall")
    f1_score: float = Field(..., description="Model F1 score")
    
    # Error rates
    error_rate: float = Field(..., description="System error rate percentage")
    timeout_rate: float = Field(..., description="Timeout rate percentage")
    
    # Resource utilization
    cpu_utilization: float = Field(..., description="Average CPU utilization percentage")
    memory_utilization: float = Field(..., description="Average memory utilization percentage")


class FraudPattern(BaseModel):
    """Fraud pattern analysis schema"""
    pattern_id: str = Field(..., description="Pattern identifier")
    pattern_type: str = Field(..., description="Type of fraud pattern")
    description: str = Field(..., description="Pattern description")
    frequency: int = Field(..., description="Pattern occurrence frequency")
    avg_amount: Decimal = Field(..., description="Average transaction amount for this pattern")
    risk_score: float = Field(..., description="Average risk score for this pattern")
    first_seen: datetime = Field(..., description="First occurrence of this pattern")
    last_seen: datetime = Field(..., description="Last occurrence of this pattern")
    
    # Pattern characteristics
    characteristics: Dict[str, Any] = Field(..., description="Pattern characteristics")
    
    # Related transactions
    sample_transaction_ids: List[str] = Field(..., description="Sample transaction IDs showing this pattern")


class ModelPerformanceReport(BaseModel):
    """ML model performance report schema"""
    model_version: str = Field(..., description="Model version")
    evaluation_period: str = Field(..., description="Evaluation period")
    
    # Performance metrics
    accuracy: float = Field(..., description="Model accuracy")
    precision: float = Field(..., description="Model precision")
    recall: float = Field(..., description="Model recall")
    f1_score: float = Field(..., description="Model F1 score")
    auc_roc: float = Field(..., description="Area under ROC curve")
    
    # Confusion matrix
    true_positives: int = Field(..., description="True positive count")
    false_positives: int = Field(..., description="False positive count")
    true_negatives: int = Field(..., description="True negative count")
    false_negatives: int = Field(..., description="False negative count")
    
    # Feature importance
    feature_importance: List[Dict[str, Any]] = Field(..., description="Feature importance scores")
    
    # Performance by transaction type
    performance_by_type: Dict[str, Dict[str, float]] = Field(..., description="Performance metrics by transaction type")
    
    # Drift detection
    data_drift_score: float = Field(..., description="Data drift score")
    concept_drift_score: float = Field(..., description="Concept drift score")
    
    # Recommendations
    recommendations: List[str] = Field(..., description="Model improvement recommendations")
