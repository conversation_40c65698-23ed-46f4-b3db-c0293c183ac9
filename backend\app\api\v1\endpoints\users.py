"""
User management endpoints
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db_session
from app.core.logging import get_logger
from app.schemas.auth import (
    UserResponse,
    UserCreate,
    UserUpdate,
    PasswordChange,
    ApiKeyCreate,
    ApiKeyResponse
)
from app.services.user_service import UserService
from app.api.dependencies import get_current_user, require_permission

router = APIRouter()
logger = get_logger(__name__)


@router.get("/", response_model=List[UserResponse])
async def list_users(
    skip: int = Query(0, ge=0, description="Number of users to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of users to return"),
    role: Optional[str] = Query(None, description="Filter by user role"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(require_permission("read:users"))
):
    """
    List users with optional filtering
    
    Returns paginated list of users. Requires 'read:users' permission.
    """
    try:
        user_service = UserService(db)
        users = await user_service.list_users(
            skip=skip,
            limit=limit,
            role=role,
            is_active=is_active
        )
        
        return [UserResponse.from_orm(user) for user in users]
        
    except Exception as e:
        logger.error(
            "Failed to list users",
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve users"
        )


@router.post("/", response_model=UserResponse)
async def create_user(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(require_permission("write:users"))
):
    """
    Create a new user
    
    Creates a new user account. Requires 'write:users' permission.
    """
    try:
        user_service = UserService(db)
        
        # Check if user already exists
        existing_user = await user_service.get_user_by_email(user_data.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User with this email already exists"
            )
        
        # Create user
        user = await user_service.create_user(
            email=user_data.email,
            password=user_data.password,
            full_name=user_data.full_name,
            role=user_data.role
        )
        
        logger.info(
            "User created",
            user_id=user.id,
            email=user.email,
            created_by=current_user.id
        )
        
        return UserResponse.from_orm(user)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to create user",
            email=user_data.email,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user"
        )


@router.get("/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: UUID,
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(get_current_user)
):
    """
    Get user by ID
    
    Returns user information. Users can access their own info, others require 'read:users' permission.
    """
    try:
        user_service = UserService(db)
        
        # Check if user is accessing their own info or has permission
        if user_id != current_user.id:
            # Check permission for accessing other users
            from app.core.security import check_permissions
            if not check_permissions(current_user.role, "read:users"):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Not enough permissions"
                )
        
        user = await user_service.get_user(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        return UserResponse.from_orm(user)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to get user",
            user_id=user_id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user"
        )


@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: UUID,
    user_data: UserUpdate,
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(get_current_user)
):
    """
    Update user information
    
    Updates user data. Users can update their own info (limited), others require 'write:users' permission.
    """
    try:
        user_service = UserService(db)
        
        # Check permissions
        if user_id != current_user.id:
            from app.core.security import check_permissions
            if not check_permissions(current_user.role, "write:users"):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Not enough permissions"
                )
        else:
            # Users can only update their own name, not role or active status
            if user_data.role is not None or user_data.is_active is not None:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Cannot modify role or active status"
                )
        
        # Get existing user
        user = await user_service.get_user(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Update user
        updated_user = await user_service.update_user(user_id, user_data)
        
        logger.info(
            "User updated",
            user_id=user_id,
            updated_by=current_user.id
        )
        
        return UserResponse.from_orm(updated_user)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to update user",
            user_id=user_id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user"
        )


@router.delete("/{user_id}")
async def delete_user(
    user_id: UUID,
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(require_permission("write:users"))
):
    """
    Delete user (soft delete)
    
    Deactivates a user account. Requires 'write:users' permission.
    """
    try:
        user_service = UserService(db)
        
        # Prevent self-deletion
        if user_id == current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete your own account"
            )
        
        # Get user
        user = await user_service.get_user(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Soft delete (deactivate)
        await user_service.deactivate_user(user_id)
        
        logger.info(
            "User deleted",
            user_id=user_id,
            deleted_by=current_user.id
        )
        
        return {"message": "User deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to delete user",
            user_id=user_id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete user"
        )


@router.post("/{user_id}/change-password")
async def change_password(
    user_id: UUID,
    password_data: PasswordChange,
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(get_current_user)
):
    """
    Change user password
    
    Changes user password. Users can change their own password, others require 'write:users' permission.
    """
    try:
        user_service = UserService(db)
        
        # Check permissions
        if user_id != current_user.id:
            from app.core.security import check_permissions
            if not check_permissions(current_user.role, "write:users"):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Not enough permissions"
                )
        
        # Change password
        success = await user_service.change_password(
            user_id,
            password_data.current_password,
            password_data.new_password
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Current password is incorrect"
            )
        
        logger.info(
            "Password changed",
            user_id=user_id,
            changed_by=current_user.id
        )
        
        return {"message": "Password changed successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to change password",
            user_id=user_id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to change password"
        )
