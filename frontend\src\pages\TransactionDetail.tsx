import React, { useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { ArrowLeftIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { useTransactionStore } from '@/store/transactionStore';
import { useAlertStore } from '@/store/alertStore';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import {
  formatCurrency,
  formatDateTime,
  getRiskLevelColor,
  getFraudDecisionColor,
  getAlertSeverityColor,
} from '@/utils';

const TransactionDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const {
    currentTransaction,
    isLoading: transactionLoading,
    error: transactionError,
    fetchTransaction,
  } = useTransactionStore();

  const {
    alerts,
    fetchAlerts,
  } = useAlertStore();

  useEffect(() => {
    if (id) {
      fetchTransaction(id);
      fetchAlerts({ transaction_id: id });
    }
  }, [id, fetchTransaction, fetchAlerts]);

  if (transactionLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (transactionError || !currentTransaction) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => navigate(-1)}>
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h2 className="text-2xl font-bold text-gray-900">Transaction Not Found</h2>
        </div>
        
        <Card>
          <div className="text-center py-12">
            <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Transaction not found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {transactionError || 'The requested transaction could not be found.'}
            </p>
            <div className="mt-6">
              <Link to="/transactions">
                <Button>View All Transactions</Button>
              </Link>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  const relatedAlerts = alerts.filter(alert => alert.transaction_id === currentTransaction.transaction_id);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => navigate(-1)}>
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Transaction Details</h2>
            <p className="text-sm text-gray-500">ID: {currentTransaction.transaction_id}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          {currentTransaction.fraud_result && (
            <>
              <Badge className={getRiskLevelColor(currentTransaction.fraud_result.risk_level)}>
                {currentTransaction.fraud_result.risk_level} Risk
              </Badge>
              <Badge className={getFraudDecisionColor(currentTransaction.fraud_result.decision)}>
                {currentTransaction.fraud_result.decision}
              </Badge>
            </>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Transaction Information */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <Card.Header>
              <h3 className="text-lg font-medium text-gray-900">Transaction Information</h3>
            </Card.Header>
            <Card.Body>
              <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Transaction ID</dt>
                  <dd className="mt-1 text-sm text-gray-900">{currentTransaction.transaction_id}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Step</dt>
                  <dd className="mt-1 text-sm text-gray-900">{currentTransaction.step}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Type</dt>
                  <dd className="mt-1">
                    <Badge>{currentTransaction.type}</Badge>
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Amount</dt>
                  <dd className="mt-1 text-lg font-semibold text-gray-900">
                    {formatCurrency(currentTransaction.amount)}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Created</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {formatDateTime(currentTransaction.created_at)}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Updated</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {formatDateTime(currentTransaction.updated_at)}
                  </dd>
                </div>
              </dl>
            </Card.Body>
          </Card>

          {/* Account Information */}
          <Card>
            <Card.Header>
              <h3 className="text-lg font-medium text-gray-900">Account Information</h3>
            </Card.Header>
            <Card.Body>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Origin Account */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Origin Account</h4>
                  <dl className="space-y-3">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Account</dt>
                      <dd className="mt-1 text-sm text-gray-900 font-mono">
                        {currentTransaction.name_orig}
                      </dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Old Balance</dt>
                      <dd className="mt-1 text-sm text-gray-900">
                        {formatCurrency(currentTransaction.oldbalance_orig)}
                      </dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">New Balance</dt>
                      <dd className="mt-1 text-sm text-gray-900">
                        {formatCurrency(currentTransaction.newbalance_orig)}
                      </dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Balance Change</dt>
                      <dd className={`mt-1 text-sm font-medium ${
                        currentTransaction.newbalance_orig - currentTransaction.oldbalance_orig < 0
                          ? 'text-red-600'
                          : 'text-green-600'
                      }`}>
                        {formatCurrency(currentTransaction.newbalance_orig - currentTransaction.oldbalance_orig)}
                      </dd>
                    </div>
                  </dl>
                </div>

                {/* Destination Account */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Destination Account</h4>
                  <dl className="space-y-3">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Account</dt>
                      <dd className="mt-1 text-sm text-gray-900 font-mono">
                        {currentTransaction.name_dest}
                      </dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Old Balance</dt>
                      <dd className="mt-1 text-sm text-gray-900">
                        {formatCurrency(currentTransaction.oldbalance_dest)}
                      </dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">New Balance</dt>
                      <dd className="mt-1 text-sm text-gray-900">
                        {formatCurrency(currentTransaction.newbalance_dest)}
                      </dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Balance Change</dt>
                      <dd className={`mt-1 text-sm font-medium ${
                        currentTransaction.newbalance_dest - currentTransaction.oldbalance_dest > 0
                          ? 'text-green-600'
                          : 'text-red-600'
                      }`}>
                        {formatCurrency(currentTransaction.newbalance_dest - currentTransaction.oldbalance_dest)}
                      </dd>
                    </div>
                  </dl>
                </div>
              </div>
            </Card.Body>
          </Card>
        </div>

        {/* Fraud Analysis & Alerts */}
        <div className="space-y-6">
          {/* Fraud Analysis */}
          {currentTransaction.fraud_result && (
            <Card>
              <Card.Header>
                <h3 className="text-lg font-medium text-gray-900">Fraud Analysis</h3>
              </Card.Header>
              <Card.Body>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900">
                      {(currentTransaction.fraud_result.fraud_score * 100).toFixed(1)}%
                    </div>
                    <div className="text-sm text-gray-500">Fraud Score</div>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Risk Level</dt>
                      <dd className="mt-1">
                        <Badge className={getRiskLevelColor(currentTransaction.fraud_result.risk_level)}>
                          {currentTransaction.fraud_result.risk_level}
                        </Badge>
                      </dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Decision</dt>
                      <dd className="mt-1">
                        <Badge className={getFraudDecisionColor(currentTransaction.fraud_result.decision)}>
                          {currentTransaction.fraud_result.decision}
                        </Badge>
                      </dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Fraudulent</dt>
                      <dd className="mt-1">
                        <Badge variant={currentTransaction.fraud_result.is_fraudulent ? 'danger' : 'success'}>
                          {currentTransaction.fraud_result.is_fraudulent ? 'Yes' : 'No'}
                        </Badge>
                      </dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Processing Time</dt>
                      <dd className="mt-1 text-sm text-gray-900">
                        {currentTransaction.fraud_result.processing_time_ms}ms
                      </dd>
                    </div>
                    {currentTransaction.fraud_result.reason_codes.length > 0 && (
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Reason Codes</dt>
                        <dd className="mt-1">
                          <div className="flex flex-wrap gap-1">
                            {currentTransaction.fraud_result.reason_codes.map((code, index) => (
                              <Badge key={index} variant="info" size="sm">
                                {code}
                              </Badge>
                            ))}
                          </div>
                        </dd>
                      </div>
                    )}
                  </div>
                </div>
              </Card.Body>
            </Card>
          )}

          {/* Related Alerts */}
          <Card>
            <Card.Header>
              <h3 className="text-lg font-medium text-gray-900">Related Alerts</h3>
            </Card.Header>
            <Card.Body>
              {relatedAlerts.length > 0 ? (
                <div className="space-y-3">
                  {relatedAlerts.map((alert) => (
                    <div key={alert.id} className="border rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="text-sm font-medium text-gray-900">{alert.title}</h4>
                        <Badge className={getAlertSeverityColor(alert.severity)} size="sm">
                          {alert.severity}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{alert.description}</p>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500">
                          {formatDateTime(alert.created_at)}
                        </span>
                        <Link
                          to={`/alerts/${alert.id}`}
                          className="text-xs text-primary-600 hover:text-primary-500"
                        >
                          View Details
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 text-gray-500">
                  <ExclamationTriangleIcon className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                  <p className="text-sm">No alerts for this transaction</p>
                </div>
              )}
            </Card.Body>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default TransactionDetail;
