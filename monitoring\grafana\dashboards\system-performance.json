{"dashboard": {"id": null, "title": "System Performance Dashboard", "tags": ["performance", "system", "monitoring"], "style": "dark", "timezone": "browser", "panels": [{"id": 1, "title": "CPU Usage", "type": "graph", "targets": [{"expr": "100 - (avg by(instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)", "legendFormat": "{{instance}}"}], "yAxes": [{"min": 0, "max": 100, "unit": "percent"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Memory Usage", "type": "graph", "targets": [{"expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100", "legendFormat": "{{instance}}"}], "yAxes": [{"min": 0, "max": 100, "unit": "percent"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Request Rate", "type": "graph", "targets": [{"expr": "rate(http_requests_total[5m])", "legendFormat": "{{job}} - {{method}}"}], "yAxes": [{"min": 0, "unit": "reqps"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Response Time", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "50th percentile"}], "yAxes": [{"min": 0, "unit": "s"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "Error Rate", "type": "graph", "targets": [{"expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m]) * 100", "legendFormat": "{{job}}"}], "yAxes": [{"min": 0, "unit": "percent"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "5s"}}