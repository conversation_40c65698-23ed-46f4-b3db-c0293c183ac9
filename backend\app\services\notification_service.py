"""
Notification Service for sending alerts via various channels
"""

import asyncio
import aiohttp
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from typing import Dict, Any, List, Optional

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class NotificationService:
    """Service for sending notifications through various channels"""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def initialize(self):
        """Initialize notification service"""
        try:
            self.session = aiohttp.ClientSession()
            logger.info("Notification service initialized")
        except Exception as e:
            logger.error("Failed to initialize notification service", error=str(e))
    
    async def close(self):
        """Close notification service"""
        if self.session:
            await self.session.close()
            self.session = None
    
    async def send_slack_alert(
        self,
        message: str,
        alert_data: Dict[str, Any],
        channel: Optional[str] = None
    ) -> bool:
        """
        Send alert to Slack
        
        Args:
            message: Alert message
            alert_data: Alert data for formatting
            channel: Slack channel (optional)
            
        Returns:
            True if successfully sent
        """
        try:
            if not settings.SLACK_WEBHOOK_URL:
                logger.warning("Slack webhook URL not configured")
                return False
            
            if not self.session:
                await self.initialize()
            
            # Format Slack message
            slack_payload = self._format_slack_message(message, alert_data, channel)
            
            async with self.session.post(
                settings.SLACK_WEBHOOK_URL,
                json=slack_payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status == 200:
                    logger.info(
                        "Slack alert sent successfully",
                        alert_id=alert_data.get("alert_id"),
                        transaction_id=alert_data.get("transaction_id")
                    )
                    return True
                else:
                    logger.error(
                        "Failed to send Slack alert",
                        status=response.status,
                        response=await response.text()
                    )
                    return False
                    
        except Exception as e:
            logger.error(
                "Error sending Slack alert",
                alert_id=alert_data.get("alert_id"),
                error=str(e)
            )
            return False
    
    def _format_slack_message(
        self,
        message: str,
        alert_data: Dict[str, Any],
        channel: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Format message for Slack
        
        Args:
            message: Alert message
            alert_data: Alert data
            channel: Slack channel
            
        Returns:
            Slack payload
        """
        severity = alert_data.get("severity", "MEDIUM")
        
        # Choose color based on severity
        color_map = {
            "CRITICAL": "#FF0000",  # Red
            "HIGH": "#FF8C00",      # Orange
            "MEDIUM": "#FFD700",    # Yellow
            "LOW": "#32CD32"        # Green
        }
        
        color = color_map.get(severity, "#FFD700")
        
        # Create rich Slack message
        payload = {
            "text": f"🚨 Fraud Alert - {severity} Risk",
            "attachments": [
                {
                    "color": color,
                    "title": f"Fraud Detection Alert - {severity} Risk",
                    "text": message,
                    "fields": [
                        {
                            "title": "Transaction ID",
                            "value": alert_data.get("transaction_id", "N/A"),
                            "short": True
                        },
                        {
                            "title": "Fraud Score",
                            "value": f"{alert_data.get('fraud_score', 0):.3f}",
                            "short": True
                        },
                        {
                            "title": "Amount",
                            "value": f"${alert_data.get('transaction_amount', 0):,.2f}",
                            "short": True
                        },
                        {
                            "title": "Decision",
                            "value": alert_data.get("decision", "N/A"),
                            "short": True
                        }
                    ],
                    "footer": "FraudShield Alert System",
                    "ts": int(alert_data.get("timestamp", 0))
                }
            ]
        }
        
        if channel:
            payload["channel"] = channel
        
        return payload
    
    async def send_email_alert(
        self,
        subject: str,
        message: str,
        alert_data: Dict[str, Any],
        recipients: Optional[List[str]] = None
    ) -> bool:
        """
        Send alert via email
        
        Args:
            subject: Email subject
            message: Alert message
            alert_data: Alert data
            recipients: Email recipients (optional)
            
        Returns:
            True if successfully sent
        """
        try:
            if not settings.SMTP_HOST:
                logger.warning("SMTP not configured")
                return False
            
            # Default recipients (would typically come from configuration)
            if not recipients:
                recipients = ["<EMAIL>"]  # Configure as needed
            
            # Create email message
            msg = MIMEMultipart()
            msg['From'] = settings.SMTP_USERNAME or "<EMAIL>"
            msg['To'] = ", ".join(recipients)
            msg['Subject'] = subject
            
            # Format email body
            email_body = self._format_email_message(message, alert_data)
            msg.attach(MIMEText(email_body, 'html'))
            
            # Send email
            await self._send_email(msg, recipients)
            
            logger.info(
                "Email alert sent successfully",
                alert_id=alert_data.get("alert_id"),
                recipients=recipients
            )
            return True
            
        except Exception as e:
            logger.error(
                "Error sending email alert",
                alert_id=alert_data.get("alert_id"),
                error=str(e)
            )
            return False
    
    def _format_email_message(self, message: str, alert_data: Dict[str, Any]) -> str:
        """
        Format message for email
        
        Args:
            message: Alert message
            alert_data: Alert data
            
        Returns:
            HTML formatted email body
        """
        severity = alert_data.get("severity", "MEDIUM")
        
        # Choose color based on severity
        color_map = {
            "CRITICAL": "#FF0000",
            "HIGH": "#FF8C00",
            "MEDIUM": "#FFD700",
            "LOW": "#32CD32"
        }
        
        color = color_map.get(severity, "#FFD700")
        
        html_body = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; }}
                .alert-header {{ background-color: {color}; color: white; padding: 10px; }}
                .alert-content {{ padding: 20px; }}
                .alert-details {{ background-color: #f5f5f5; padding: 15px; margin: 10px 0; }}
                .highlight {{ font-weight: bold; }}
            </style>
        </head>
        <body>
            <div class="alert-header">
                <h2>🚨 FraudShield Alert - {severity} Risk</h2>
            </div>
            <div class="alert-content">
                <p>A {severity.lower()}-risk fraudulent transaction has been detected.</p>
                
                <div class="alert-details">
                    <h3>Transaction Details</h3>
                    <p><span class="highlight">Transaction ID:</span> {alert_data.get('transaction_id', 'N/A')}</p>
                    <p><span class="highlight">Fraud Score:</span> {alert_data.get('fraud_score', 0):.3f}</p>
                    <p><span class="highlight">Amount:</span> ${alert_data.get('transaction_amount', 0):,.2f}</p>
                    <p><span class="highlight">Type:</span> {alert_data.get('transaction_type', 'N/A')}</p>
                    <p><span class="highlight">Decision:</span> {alert_data.get('decision', 'N/A')}</p>
                    <p><span class="highlight">Risk Level:</span> {alert_data.get('risk_level', 'N/A')}</p>
                </div>
                
                <div class="alert-details">
                    <h3>Fraud Indicators</h3>
                    <ul>
                        {''.join(f'<li>{reason}</li>' for reason in alert_data.get('reasons', []))}
                    </ul>
                </div>
                
                <p><span class="highlight">Alert ID:</span> {alert_data.get('alert_id', 'N/A')}</p>
                <p><span class="highlight">Timestamp:</span> {alert_data.get('timestamp', 'N/A')}</p>
                
                {'<p style="color: red; font-weight: bold;">⚠️ REQUIRES IMMEDIATE ATTENTION</p>' if alert_data.get('requires_immediate_attention') else ''}
            </div>
        </body>
        </html>
        """
        
        return html_body
    
    async def _send_email(self, msg: MIMEMultipart, recipients: List[str]):
        """
        Send email using SMTP
        
        Args:
            msg: Email message
            recipients: Email recipients
        """
        # Run in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, self._send_email_sync, msg, recipients)
    
    def _send_email_sync(self, msg: MIMEMultipart, recipients: List[str]):
        """
        Synchronous email sending
        
        Args:
            msg: Email message
            recipients: Email recipients
        """
        try:
            with smtplib.SMTP(settings.SMTP_HOST, settings.SMTP_PORT) as server:
                if settings.SMTP_USERNAME and settings.SMTP_PASSWORD:
                    server.starttls()
                    server.login(settings.SMTP_USERNAME, settings.SMTP_PASSWORD)
                
                server.send_message(msg, to_addrs=recipients)
                
        except Exception as e:
            logger.error("Failed to send email", error=str(e))
            raise
    
    async def send_sms_alert(
        self,
        message: str,
        phone_numbers: List[str],
        alert_data: Dict[str, Any]
    ) -> bool:
        """
        Send SMS alert (placeholder for SMS service integration)
        
        Args:
            message: Alert message
            phone_numbers: Phone numbers to send to
            alert_data: Alert data
            
        Returns:
            True if successfully sent
        """
        try:
            # This would integrate with SMS service like Twilio
            logger.info(
                "SMS alert would be sent",
                alert_id=alert_data.get("alert_id"),
                recipients=phone_numbers,
                message_preview=message[:50]
            )
            return True
            
        except Exception as e:
            logger.error(
                "Error sending SMS alert",
                alert_id=alert_data.get("alert_id"),
                error=str(e)
            )
            return False
