"""
Alert management schemas
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field


class AlertBase(BaseModel):
    """Base alert schema"""
    transaction_id: str = Field(..., description="Related transaction ID")
    alert_type: str = Field(..., description="Type of alert")
    severity: str = Field(..., description="Alert severity level")
    title: str = Field(..., description="Alert title")
    description: str = Field(..., description="Alert description")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional alert metadata")


class AlertCreate(AlertBase):
    """Alert creation schema"""
    pass


class AlertUpdate(BaseModel):
    """Alert update schema"""
    status: Optional[str] = Field(None, description="Alert status")
    severity: Optional[str] = Field(None, description="Alert severity")
    resolution_notes: Optional[str] = Field(None, description="Resolution notes")
    assigned_to: Optional[UUID] = Field(None, description="User assigned to handle alert")


class AlertResponse(AlertBase):
    """Alert response schema"""
    id: UUID = Field(..., description="Alert ID")
    status: str = Field(..., description="Alert status")
    fraud_score: Optional[float] = Field(None, description="Associated fraud score")
    risk_level: Optional[str] = Field(None, description="Risk level")
    
    # Timestamps
    created_at: datetime = Field(..., description="Alert creation timestamp")
    acknowledged_at: Optional[datetime] = Field(None, description="Alert acknowledgment timestamp")
    resolved_at: Optional[datetime] = Field(None, description="Alert resolution timestamp")
    
    # User assignments
    created_by: Optional[UUID] = Field(None, description="User who created the alert")
    acknowledged_by: Optional[UUID] = Field(None, description="User who acknowledged the alert")
    resolved_by: Optional[UUID] = Field(None, description="User who resolved the alert")
    assigned_to: Optional[UUID] = Field(None, description="User assigned to handle alert")
    
    # Resolution
    resolution_notes: Optional[str] = Field(None, description="Resolution notes")
    
    class Config:
        from_attributes = True


class AlertStats(BaseModel):
    """Alert statistics schema"""
    total_alerts: int = Field(..., description="Total number of alerts")
    open_alerts: int = Field(..., description="Number of open alerts")
    resolved_alerts: int = Field(..., description="Number of resolved alerts")
    avg_resolution_time_hours: float = Field(..., description="Average resolution time in hours")
    
    # Breakdown by severity
    alerts_by_severity: Dict[str, int] = Field(..., description="Alert count by severity")
    
    # Breakdown by status
    alerts_by_status: Dict[str, int] = Field(..., description="Alert count by status")
    
    # Breakdown by type
    alerts_by_type: Dict[str, int] = Field(..., description="Alert count by type")
    
    # Trends
    daily_alert_counts: List[Dict[str, Any]] = Field(..., description="Daily alert counts")
    
    # Performance metrics
    false_positive_rate: float = Field(..., description="False positive rate percentage")
    true_positive_rate: float = Field(..., description="True positive rate percentage")
