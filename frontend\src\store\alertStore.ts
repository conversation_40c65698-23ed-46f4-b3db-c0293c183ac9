import { create } from 'zustand';
import { apiClient } from '@/services/api';
import { wsService } from '@/services/websocket';
import type { Alert, PaginatedResponse, RealTimeAlert } from '@/types';

interface AlertState {
  alerts: Alert[];
  currentAlert: Alert | null;
  realtimeAlerts: RealTimeAlert[];
  totalCount: number;
  currentPage: number;
  pageSize: number;
  isLoading: boolean;
  error: string | null;
  filters: {
    status?: string;
    severity?: string;
    alert_type?: string;
  };

  // Actions
  fetchAlerts: (params?: any) => Promise<void>;
  fetchAlert: (id: string) => Promise<void>;
  updateAlertStatus: (id: string, status: string, notes?: string) => Promise<void>;
  createAlert: (data: any) => Promise<void>;
  setFilters: (filters: any) => void;
  setPage: (page: number) => void;
  setPageSize: (size: number) => void;
  clearError: () => void;
  addRealtimeAlert: (alert: RealTimeAlert) => void;
  clearRealtimeAlerts: () => void;
  markAlertAsRead: (id: string) => void;
}

export const useAlertStore = create<AlertState>((set, get) => ({
  alerts: [],
  currentAlert: null,
  realtimeAlerts: [],
  totalCount: 0,
  currentPage: 1,
  pageSize: 20,
  isLoading: false,
  error: null,
  filters: {},

  fetchAlerts: async (params?: any) => {
    set({ isLoading: true, error: null });
    
    try {
      const { currentPage, pageSize, filters } = get();
      const queryParams = {
        skip: (currentPage - 1) * pageSize,
        limit: pageSize,
        ...filters,
        ...params,
      };

      const response: PaginatedResponse<Alert> = await apiClient.getAlerts(queryParams);
      
      set({
        alerts: response.data,
        totalCount: response.total,
        currentPage: response.page,
        isLoading: false,
        error: null,
      });
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.response?.data?.detail || error.message || 'Failed to fetch alerts',
      });
    }
  },

  fetchAlert: async (id: string) => {
    set({ isLoading: true, error: null });
    
    try {
      const alert = await apiClient.getAlert(id);
      
      set({
        currentAlert: alert,
        isLoading: false,
        error: null,
      });
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.response?.data?.detail || error.message || 'Failed to fetch alert',
      });
    }
  },

  updateAlertStatus: async (id: string, status: string, notes?: string) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedAlert = await apiClient.updateAlertStatus(id, status, notes);
      
      set((state) => ({
        alerts: state.alerts.map(alert => 
          alert.id === id ? updatedAlert : alert
        ),
        currentAlert: state.currentAlert?.id === id ? updatedAlert : state.currentAlert,
        isLoading: false,
        error: null,
      }));
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.response?.data?.detail || error.message || 'Failed to update alert',
      });
      throw error;
    }
  },

  createAlert: async (data: any) => {
    set({ isLoading: true, error: null });
    
    try {
      const alert = await apiClient.createAlert(data);
      
      set((state) => ({
        alerts: [alert, ...state.alerts],
        totalCount: state.totalCount + 1,
        isLoading: false,
        error: null,
      }));
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.response?.data?.detail || error.message || 'Failed to create alert',
      });
      throw error;
    }
  },

  setFilters: (filters: any) => {
    set({ filters, currentPage: 1 });
    get().fetchAlerts();
  },

  setPage: (page: number) => {
    set({ currentPage: page });
    get().fetchAlerts();
  },

  setPageSize: (size: number) => {
    set({ pageSize: size, currentPage: 1 });
    get().fetchAlerts();
  },

  clearError: () => {
    set({ error: null });
  },

  addRealtimeAlert: (alert: RealTimeAlert) => {
    set((state) => ({
      realtimeAlerts: [alert, ...state.realtimeAlerts.slice(0, 49)], // Keep last 50
    }));
  },

  clearRealtimeAlerts: () => {
    set({ realtimeAlerts: [] });
  },

  markAlertAsRead: (id: string) => {
    set((state) => ({
      realtimeAlerts: state.realtimeAlerts.map(alert => 
        alert.alert.id === id ? { ...alert, read: true } : alert
      ),
    }));
  },
}));

// Set up real-time alert updates
wsService.on('alert', (data: RealTimeAlert) => {
  useAlertStore.getState().addRealtimeAlert(data);
});
