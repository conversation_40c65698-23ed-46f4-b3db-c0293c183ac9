apiVersion: v2
name: fraudshield
description: A Helm chart for FraudShield fraud detection system
type: application
version: 1.0.0
appVersion: "1.0.0"
home: https://github.com/fraudshield/fraudshield
sources:
  - https://github.com/fraudshield/fraudshield
maintainers:
  - name: MLOps Team
    email: <EMAIL>
keywords:
  - fraud-detection
  - machine-learning
  - fintech
  - real-time
  - microservices
annotations:
  category: Analytics
  licenses: Apache-2.0

dependencies:
  - name: postgresql
    version: 12.1.9
    repository: https://charts.bitnami.com/bitnami
    condition: postgresql.enabled
    tags:
      - database
  
  - name: redis
    version: 17.3.7
    repository: https://charts.bitnami.com/bitnami
    condition: redis.enabled
    tags:
      - cache
  
  - name: kafka
    version: 20.0.6
    repository: https://charts.bitnami.com/bitnami
    condition: kafka.enabled
    tags:
      - streaming
  
  - name: elasticsearch
    version: 19.5.0
    repository: https://charts.bitnami.com/bitnami
    condition: elasticsearch.enabled
    tags:
      - logging
  
  - name: prometheus
    version: 15.18.0
    repository: https://prometheus-community.github.io/helm-charts
    condition: prometheus.enabled
    tags:
      - monitoring
  
  - name: grafana
    version: 6.50.7
    repository: https://grafana.github.io/helm-charts
    condition: grafana.enabled
    tags:
      - monitoring
  
  - name: mlflow
    version: 0.7.19
    repository: https://community-charts.github.io/helm-charts
    condition: mlflow.enabled
    tags:
      - mlops
