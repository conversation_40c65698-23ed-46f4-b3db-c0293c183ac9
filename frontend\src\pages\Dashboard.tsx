import React, { useEffect } from 'react';
import { useDashboardStore } from '@/store/dashboardStore';
import { useTransactionStore } from '@/store/transactionStore';
import { useAlertStore } from '@/store/alertStore';
import StatsCards from '@/components/dashboard/StatsCards';
import FraudTrendsChart from '@/components/dashboard/FraudTrendsChart';
import RecentTransactions from '@/components/dashboard/RecentTransactions';
import RecentAlerts from '@/components/dashboard/RecentAlerts';
import RealTimeActivity from '@/components/dashboard/RealTimeActivity';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

const Dashboard: React.FC = () => {
  const {
    stats,
    isLoading: dashboardLoading,
    error: dashboardError,
    fetchDashboardStats,
    fetchTransactionStats,
    fetchFraudTrends,
  } = useDashboardStore();

  const {
    transactions,
    isLoading: transactionsLoading,
    fetchTransactions,
  } = useTransactionStore();

  const {
    alerts,
    isLoading: alertsLoading,
    fetchAlerts,
  } = useAlertStore();

  useEffect(() => {
    // Fetch initial data
    const loadDashboardData = async () => {
      await Promise.all([
        fetchDashboardStats(),
        fetchTransactionStats({ period: '24h' }),
        fetchFraudTrends({ period: '7d' }),
        fetchTransactions({ limit: 10 }),
        fetchAlerts({ limit: 10, status: 'OPEN' }),
      ]);
    };

    loadDashboardData();
  }, [
    fetchDashboardStats,
    fetchTransactionStats,
    fetchFraudTrends,
    fetchTransactions,
    fetchAlerts,
  ]);

  if (dashboardLoading && !stats) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (dashboardError) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-red-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">
              Error loading dashboard
            </h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{dashboardError}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Dashboard
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Real-time fraud detection overview and system metrics
          </p>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <button
            type="button"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            onClick={() => {
              fetchDashboardStats();
              fetchTransactionStats({ period: '24h' });
              fetchFraudTrends({ period: '7d' });
            }}
          >
            <svg
              className="mr-2 h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            Refresh
          </button>
        </div>
      </div>

      {/* Stats cards */}
      <StatsCards stats={stats} />

      {/* Charts and activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Fraud trends chart */}
        <div className="lg:col-span-2">
          <FraudTrendsChart />
        </div>

        {/* Real-time activity */}
        <div className="lg:col-span-1">
          <RealTimeActivity />
        </div>

        {/* Recent alerts */}
        <div className="lg:col-span-1">
          <RecentAlerts
            alerts={alerts}
            isLoading={alertsLoading}
          />
        </div>
      </div>

      {/* Recent transactions */}
      <div>
        <RecentTransactions
          transactions={transactions}
          isLoading={transactionsLoading}
        />
      </div>
    </div>
  );
};

export default Dashboard;
