"""
Alert management service
"""

from typing import List, Optional, Dict, Any
from uuid import UUID, uuid4
from datetime import datetime, timedelta

from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import get_logger
from app.schemas.alert import AlertCreate, AlertUpdate, AlertResponse, AlertStats

logger = get_logger(__name__)


# Mock Alert model
class Alert:
    """Mock Alert model"""
    def __init__(self, **kwargs):
        self.id = kwargs.get('id', uuid4())
        self.transaction_id = kwargs.get('transaction_id')
        self.alert_type = kwargs.get('alert_type')
        self.severity = kwargs.get('severity')
        self.title = kwargs.get('title')
        self.description = kwargs.get('description')
        self.status = kwargs.get('status', 'OPEN')
        self.fraud_score = kwargs.get('fraud_score')
        self.risk_level = kwargs.get('risk_level')
        self.metadata = kwargs.get('metadata', {})
        
        # Timestamps
        self.created_at = kwargs.get('created_at', datetime.utcnow())
        self.acknowledged_at = kwargs.get('acknowledged_at')
        self.resolved_at = kwargs.get('resolved_at')
        
        # User assignments
        self.created_by = kwargs.get('created_by')
        self.acknowledged_by = kwargs.get('acknowledged_by')
        self.resolved_by = kwargs.get('resolved_by')
        self.assigned_to = kwargs.get('assigned_to')
        
        # Resolution
        self.resolution_notes = kwargs.get('resolution_notes')


class AlertService:
    """Service for alert management operations"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        # Mock alert storage
        self._alerts = {}
    
    async def create_alert(
        self,
        alert_data: AlertCreate,
        created_by: UUID
    ) -> AlertResponse:
        """
        Create a new alert
        
        Args:
            alert_data: Alert creation data
            created_by: User ID who created the alert
            
        Returns:
            Created alert
        """
        try:
            alert = Alert(
                transaction_id=alert_data.transaction_id,
                alert_type=alert_data.alert_type,
                severity=alert_data.severity,
                title=alert_data.title,
                description=alert_data.description,
                metadata=alert_data.metadata,
                created_by=created_by,
                status="OPEN"
            )
            
            # Store alert
            self._alerts[str(alert.id)] = alert
            
            logger.info(
                "Alert created",
                alert_id=alert.id,
                transaction_id=alert.transaction_id,
                severity=alert.severity,
                created_by=created_by
            )
            
            return self._alert_to_response(alert)
            
        except Exception as e:
            logger.error(
                "Failed to create alert",
                transaction_id=alert_data.transaction_id,
                error=str(e)
            )
            raise
    
    async def get_alert(self, alert_id: UUID) -> Optional[AlertResponse]:
        """
        Get alert by ID
        
        Args:
            alert_id: Alert ID
            
        Returns:
            Alert if found, None otherwise
        """
        try:
            alert = self._alerts.get(str(alert_id))
            
            if not alert:
                return None
            
            return self._alert_to_response(alert)
            
        except Exception as e:
            logger.error(
                "Failed to get alert",
                alert_id=alert_id,
                error=str(e)
            )
            return None
    
    async def list_alerts(
        self,
        skip: int = 0,
        limit: int = 100,
        severity: Optional[str] = None,
        status: Optional[str] = None,
        alert_type: Optional[str] = None
    ) -> List[AlertResponse]:
        """
        List alerts with optional filtering
        
        Args:
            skip: Number of alerts to skip
            limit: Maximum number of alerts to return
            severity: Filter by severity
            status: Filter by status
            alert_type: Filter by alert type
            
        Returns:
            List of alerts
        """
        try:
            alerts = list(self._alerts.values())
            
            # Apply filters
            if severity:
                alerts = [a for a in alerts if a.severity == severity]
            
            if status:
                alerts = [a for a in alerts if a.status == status]
            
            if alert_type:
                alerts = [a for a in alerts if a.alert_type == alert_type]
            
            # Sort by creation time (newest first)
            alerts.sort(key=lambda a: a.created_at, reverse=True)
            
            # Apply pagination
            alerts = alerts[skip:skip + limit]
            
            return [self._alert_to_response(alert) for alert in alerts]
            
        except Exception as e:
            logger.error(
                "Failed to list alerts",
                error=str(e)
            )
            return []
    
    async def update_alert(
        self,
        alert_id: UUID,
        alert_data: AlertUpdate,
        updated_by: UUID
    ) -> Optional[AlertResponse]:
        """
        Update alert
        
        Args:
            alert_id: Alert ID
            alert_data: Updated alert data
            updated_by: User ID who updated the alert
            
        Returns:
            Updated alert if successful, None otherwise
        """
        try:
            alert = self._alerts.get(str(alert_id))
            
            if not alert:
                return None
            
            # Update fields
            if alert_data.status is not None:
                alert.status = alert_data.status
                
                if alert_data.status == "RESOLVED":
                    alert.resolved_at = datetime.utcnow()
                    alert.resolved_by = updated_by
            
            if alert_data.severity is not None:
                alert.severity = alert_data.severity
            
            if alert_data.resolution_notes is not None:
                alert.resolution_notes = alert_data.resolution_notes
            
            if alert_data.assigned_to is not None:
                alert.assigned_to = alert_data.assigned_to
            
            logger.info(
                "Alert updated",
                alert_id=alert_id,
                status=alert.status,
                updated_by=updated_by
            )
            
            return self._alert_to_response(alert)
            
        except Exception as e:
            logger.error(
                "Failed to update alert",
                alert_id=alert_id,
                error=str(e)
            )
            return None
    
    async def acknowledge_alert(
        self,
        alert_id: UUID,
        acknowledged_by: UUID
    ) -> Optional[AlertResponse]:
        """
        Acknowledge an alert
        
        Args:
            alert_id: Alert ID
            acknowledged_by: User ID who acknowledged the alert
            
        Returns:
            Updated alert if successful, None otherwise
        """
        try:
            alert = self._alerts.get(str(alert_id))
            
            if not alert:
                return None
            
            alert.status = "ACKNOWLEDGED"
            alert.acknowledged_at = datetime.utcnow()
            alert.acknowledged_by = acknowledged_by
            
            logger.info(
                "Alert acknowledged",
                alert_id=alert_id,
                acknowledged_by=acknowledged_by
            )
            
            return self._alert_to_response(alert)
            
        except Exception as e:
            logger.error(
                "Failed to acknowledge alert",
                alert_id=alert_id,
                error=str(e)
            )
            return None
    
    async def resolve_alert(
        self,
        alert_id: UUID,
        resolution_notes: str,
        resolved_by: UUID
    ) -> Optional[AlertResponse]:
        """
        Resolve an alert
        
        Args:
            alert_id: Alert ID
            resolution_notes: Resolution notes
            resolved_by: User ID who resolved the alert
            
        Returns:
            Updated alert if successful, None otherwise
        """
        try:
            alert = self._alerts.get(str(alert_id))
            
            if not alert:
                return None
            
            alert.status = "RESOLVED"
            alert.resolved_at = datetime.utcnow()
            alert.resolved_by = resolved_by
            alert.resolution_notes = resolution_notes
            
            logger.info(
                "Alert resolved",
                alert_id=alert_id,
                resolved_by=resolved_by
            )
            
            return self._alert_to_response(alert)
            
        except Exception as e:
            logger.error(
                "Failed to resolve alert",
                alert_id=alert_id,
                error=str(e)
            )
            return None
    
    async def get_alert_stats(self, days: int = 7) -> AlertStats:
        """
        Get alert statistics
        
        Args:
            days: Number of days to include in stats
            
        Returns:
            Alert statistics
        """
        try:
            start_date = datetime.utcnow() - timedelta(days=days)
            alerts = [a for a in self._alerts.values() if a.created_at >= start_date]
            
            total_alerts = len(alerts)
            open_alerts = len([a for a in alerts if a.status == "OPEN"])
            resolved_alerts = len([a for a in alerts if a.status == "RESOLVED"])
            
            # Calculate average resolution time
            resolved_with_time = [
                a for a in alerts 
                if a.status == "RESOLVED" and a.resolved_at and a.created_at
            ]
            
            if resolved_with_time:
                total_resolution_time = sum(
                    (a.resolved_at - a.created_at).total_seconds() / 3600
                    for a in resolved_with_time
                )
                avg_resolution_time = total_resolution_time / len(resolved_with_time)
            else:
                avg_resolution_time = 0.0
            
            # Count by severity
            alerts_by_severity = {}
            for severity in ["LOW", "MEDIUM", "HIGH", "CRITICAL"]:
                alerts_by_severity[severity] = len([a for a in alerts if a.severity == severity])
            
            # Count by status
            alerts_by_status = {}
            for status in ["OPEN", "ACKNOWLEDGED", "RESOLVED", "CLOSED"]:
                alerts_by_status[status] = len([a for a in alerts if a.status == status])
            
            # Count by type
            alert_types = set(a.alert_type for a in alerts)
            alerts_by_type = {}
            for alert_type in alert_types:
                alerts_by_type[alert_type] = len([a for a in alerts if a.alert_type == alert_type])
            
            # Daily counts (mock data)
            daily_alert_counts = []
            for i in range(days):
                date = start_date + timedelta(days=i)
                count = len([a for a in alerts if a.created_at.date() == date.date()])
                daily_alert_counts.append({
                    "date": date.isoformat(),
                    "count": count
                })
            
            return AlertStats(
                total_alerts=total_alerts,
                open_alerts=open_alerts,
                resolved_alerts=resolved_alerts,
                avg_resolution_time_hours=avg_resolution_time,
                alerts_by_severity=alerts_by_severity,
                alerts_by_status=alerts_by_status,
                alerts_by_type=alerts_by_type,
                daily_alert_counts=daily_alert_counts,
                false_positive_rate=5.0,  # Mock data
                true_positive_rate=85.0   # Mock data
            )
            
        except Exception as e:
            logger.error(
                "Failed to get alert stats",
                error=str(e)
            )
            # Return empty stats
            return AlertStats(
                total_alerts=0,
                open_alerts=0,
                resolved_alerts=0,
                avg_resolution_time_hours=0.0,
                alerts_by_severity={},
                alerts_by_status={},
                alerts_by_type={},
                daily_alert_counts=[],
                false_positive_rate=0.0,
                true_positive_rate=0.0
            )
    
    def _alert_to_response(self, alert: Alert) -> AlertResponse:
        """
        Convert Alert model to AlertResponse
        
        Args:
            alert: Alert model
            
        Returns:
            AlertResponse
        """
        return AlertResponse(
            id=alert.id,
            transaction_id=alert.transaction_id,
            alert_type=alert.alert_type,
            severity=alert.severity,
            title=alert.title,
            description=alert.description,
            status=alert.status,
            fraud_score=alert.fraud_score,
            risk_level=alert.risk_level,
            metadata=alert.metadata,
            created_at=alert.created_at,
            acknowledged_at=alert.acknowledged_at,
            resolved_at=alert.resolved_at,
            created_by=alert.created_by,
            acknowledged_by=alert.acknowledged_by,
            resolved_by=alert.resolved_by,
            assigned_to=alert.assigned_to,
            resolution_notes=alert.resolution_notes
        )
