# Phase 8 Setup Script for Windows - Simple Version

Write-Host "=== FraudShield Phase 8 Setup ===" -ForegroundColor Blue

# Check if all Phase 8 components exist
$components = @{
    "Production Docker Compose" = "docker-compose.prod.yml"
    "Prometheus Config" = "monitoring\prometheus\prometheus.yml"
    "Alert Rules" = "monitoring\prometheus\alert_rules.yml"
    "Grafana Datasources" = "monitoring\grafana\datasources\datasources.yml"
    "Alertmanager Config" = "monitoring\alertmanager\alertmanager.yml"
    "Health Check Script" = "scripts\deployment\health-check.sh"
    "Deployment Script" = "scripts\deployment\deploy-production.sh"
    "Go-Live Script" = "scripts\go-live.sh"
    "Go-Live Checklist" = "docs\PHASE8_GO_LIVE_CHECKLIST.md"
    "Phase 8 Documentation" = "docs\PHASE8_COMPLETION.md"
}

Write-Host "Checking Phase 8 components..." -ForegroundColor Yellow

$allReady = $true
foreach ($item in $components.GetEnumerator()) {
    if (Test-Path $item.Value) {
        Write-Host "✓ $($item.Key)" -ForegroundColor Green
    } else {
        Write-Host "✗ $($item.Key) - Missing" -ForegroundColor Red
        $allReady = $false
    }
}

if ($allReady) {
    Write-Host "`n=== Phase 8 Setup Complete! ===" -ForegroundColor Green
    Write-Host "All components are ready for production deployment." -ForegroundColor Green
    
    Write-Host "`nNext Steps:" -ForegroundColor Yellow
    Write-Host "1. Review: docs\PHASE8_GO_LIVE_CHECKLIST.md"
    Write-Host "2. Setup secrets: bash scripts/deployment/setup-secrets.sh"
    Write-Host "3. Deploy: bash scripts/go-live.sh production latest"
    
    Write-Host "`nAccess Points (after deployment):" -ForegroundColor Cyan
    Write-Host "• Frontend: http://localhost:3000"
    Write-Host "• API: http://localhost:8000/docs"
    Write-Host "• Monitoring: http://localhost:3001"
    Write-Host "• Metrics: http://localhost:9090"
} else {
    Write-Host "`nSome components are missing. Please check the files above." -ForegroundColor Red
}

Write-Host "`n=== FraudShield Phase 8 Ready! ===" -ForegroundColor Magenta
