#!/bin/bash

# Database Backup Script for FraudShield
# This script creates automated backups of the PostgreSQL database

set -e

# Configuration
BACKUP_DIR="/backup"
DB_NAME="fraudshield"
DB_USER="fraudshield"
DB_HOST="localhost"
DB_PORT="5432"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="${BACKUP_DIR}/fraudshield_backup_${TIMESTAMP}.sql"
RETENTION_DAYS=30

# Create backup directory if it doesn't exist
mkdir -p ${BACKUP_DIR}

echo "Starting database backup at $(date)"

# Create database backup
pg_dump -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d ${DB_NAME} \
    --verbose --clean --no-owner --no-privileges \
    --file=${BACKUP_FILE}

# Compress the backup
gzip ${BAC<PERSON>UP_FILE}
COMPRESSED_FILE="${BACKUP_FILE}.gz"

echo "Database backup completed: ${COMPRESSED_FILE}"

# Verify backup integrity
if [ -f "${COMPRESSED_FILE}" ]; then
    echo "Backup file created successfully"
    echo "Backup size: $(du -h ${COMPRESSED_FILE} | cut -f1)"
else
    echo "ERROR: Backup file not found!"
    exit 1
fi

# Clean up old backups (keep only last 30 days)
find ${BACKUP_DIR} -name "fraudshield_backup_*.sql.gz" -mtime +${RETENTION_DAYS} -delete

echo "Backup cleanup completed"

# Upload to cloud storage (optional)
if [ ! -z "${AWS_S3_BUCKET}" ]; then
    echo "Uploading backup to S3..."
    aws s3 cp ${COMPRESSED_FILE} s3://${AWS_S3_BUCKET}/database-backups/
    echo "Backup uploaded to S3"
fi

echo "Database backup process completed at $(date)"
