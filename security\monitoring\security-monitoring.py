"""
Security Monitoring and SIEM Integration
Comprehensive security event monitoring and threat detection
"""

import json
import time
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import logging
import asyncio
from collections import defaultdict, deque
import geoip2.database
import geoip2.errors
from user_agents import parse as parse_user_agent
import redis
import elasticsearch
from prometheus_client import Counter, Histogram, Gauge
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import requests

logger = logging.getLogger(__name__)

class SecurityEventType(Enum):
    """Security event types"""
    LOGIN_SUCCESS = "login_success"
    LOGIN_FAILURE = "login_failure"
    LOGIN_BRUTE_FORCE = "login_brute_force"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    UNAUTHORIZED_ACCESS = "unauthorized_access"
    DATA_ACCESS_VIOLATION = "data_access_violation"
    SUSPICIOUS_TRANSACTION = "suspicious_transaction"
    API_ABUSE = "api_abuse"
    INJECTION_ATTEMPT = "injection_attempt"
    XSS_ATTEMPT = "xss_attempt"
    ACCOUNT_LOCKOUT = "account_lockout"
    PASSWORD_CHANGE = "password_change"
    MFA_BYPASS_ATTEMPT = "mfa_bypass_attempt"
    ANOMALOUS_BEHAVIOR = "anomalous_behavior"
    SYSTEM_COMPROMISE = "system_compromise"

class SecurityEventSeverity(Enum):
    """Security event severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class SecurityEvent:
    """Security event data structure"""
    event_id: str
    event_type: SecurityEventType
    severity: SecurityEventSeverity
    timestamp: datetime
    user_id: Optional[str]
    ip_address: str
    user_agent: str
    endpoint: Optional[str]
    details: Dict[str, Any]
    geolocation: Optional[Dict[str, str]] = None
    risk_score: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        data = asdict(self)
        data['event_type'] = self.event_type.value
        data['severity'] = self.severity.value
        data['timestamp'] = self.timestamp.isoformat()
        return data

class SecurityMetrics:
    """Prometheus metrics for security monitoring"""
    
    def __init__(self):
        self.security_events_total = Counter(
            'security_events_total',
            'Total number of security events',
            ['event_type', 'severity']
        )
        
        self.login_attempts_total = Counter(
            'login_attempts_total',
            'Total number of login attempts',
            ['status', 'user_id']
        )
        
        self.api_requests_total = Counter(
            'api_requests_total',
            'Total number of API requests',
            ['endpoint', 'method', 'status_code']
        )
        
        self.suspicious_activities_total = Counter(
            'suspicious_activities_total',
            'Total number of suspicious activities',
            ['activity_type']
        )
        
        self.active_sessions_gauge = Gauge(
            'active_sessions_total',
            'Number of active user sessions'
        )
        
        self.response_time_histogram = Histogram(
            'security_check_duration_seconds',
            'Time spent on security checks',
            ['check_type']
        )

class GeoLocationService:
    """IP geolocation service"""
    
    def __init__(self, geoip_db_path: str = '/usr/share/GeoIP/GeoLite2-City.mmdb'):
        try:
            self.reader = geoip2.database.Reader(geoip_db_path)
        except Exception as e:
            logger.warning(f"Failed to initialize GeoIP database: {e}")
            self.reader = None
    
    def get_location(self, ip_address: str) -> Optional[Dict[str, str]]:
        """Get geolocation for IP address"""
        if not self.reader:
            return None
        
        try:
            response = self.reader.city(ip_address)
            return {
                'country': response.country.name,
                'country_code': response.country.iso_code,
                'city': response.city.name,
                'latitude': str(response.location.latitude),
                'longitude': str(response.location.longitude),
                'timezone': response.location.time_zone
            }
        except geoip2.errors.AddressNotFoundError:
            return None
        except Exception as e:
            logger.error(f"GeoIP lookup failed for {ip_address}: {e}")
            return None

class ThreatDetector:
    """Advanced threat detection engine"""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
        self.metrics = SecurityMetrics()
        self.geolocation = GeoLocationService()
        
        # Threat detection thresholds
        self.thresholds = {
            'login_failures_per_minute': 5,
            'login_failures_per_hour': 20,
            'api_requests_per_minute': 100,
            'unique_ips_per_user_per_hour': 5,
            'failed_mfa_attempts_per_hour': 10,
        }
        
        # Suspicious patterns
        self.suspicious_patterns = {
            'sql_injection': [
                r'(\bunion\b.*\bselect\b)',
                r'(\bselect\b.*\bfrom\b)',
                r'(\binsert\b.*\binto\b)',
                r'(\bdelete\b.*\bfrom\b)',
                r'(\bdrop\b.*\btable\b)',
            ],
            'xss': [
                r'<script[^>]*>.*?</script>',
                r'javascript:',
                r'on\w+\s*=',
            ],
            'path_traversal': [
                r'\.\./',
                r'\.\.\\',
                r'/etc/passwd',
                r'/etc/shadow',
            ]
        }
    
    def analyze_login_attempt(self, user_id: str, ip_address: str, 
                            success: bool, user_agent: str) -> List[SecurityEvent]:
        """Analyze login attempt for threats"""
        events = []
        current_time = datetime.utcnow()
        
        # Track login attempts
        key_minute = f"login_attempts:{user_id}:{ip_address}:{current_time.strftime('%Y%m%d%H%M')}"
        key_hour = f"login_attempts:{user_id}:{ip_address}:{current_time.strftime('%Y%m%d%H')}"
        
        attempts_minute = self.redis.incr(key_minute)
        attempts_hour = self.redis.incr(key_hour)
        
        # Set expiration
        self.redis.expire(key_minute, 60)
        self.redis.expire(key_hour, 3600)
        
        # Check for brute force
        if not success:
            if attempts_minute > self.thresholds['login_failures_per_minute']:
                events.append(SecurityEvent(
                    event_id=self._generate_event_id(),
                    event_type=SecurityEventType.LOGIN_BRUTE_FORCE,
                    severity=SecurityEventSeverity.HIGH,
                    timestamp=current_time,
                    user_id=user_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    endpoint='/auth/login',
                    details={
                        'attempts_per_minute': attempts_minute,
                        'attempts_per_hour': attempts_hour,
                        'threshold_exceeded': 'login_failures_per_minute'
                    },
                    geolocation=self.geolocation.get_location(ip_address)
                ))
        
        # Check for distributed attacks
        if not success and attempts_hour > self.thresholds['login_failures_per_hour']:
            events.append(SecurityEvent(
                event_id=self._generate_event_id(),
                event_type=SecurityEventType.LOGIN_BRUTE_FORCE,
                severity=SecurityEventSeverity.CRITICAL,
                timestamp=current_time,
                user_id=user_id,
                ip_address=ip_address,
                user_agent=user_agent,
                endpoint='/auth/login',
                details={
                    'attempts_per_hour': attempts_hour,
                    'threshold_exceeded': 'login_failures_per_hour',
                    'attack_type': 'distributed_brute_force'
                },
                geolocation=self.geolocation.get_location(ip_address)
            ))
        
        # Check for unusual geolocation
        if success:
            location = self.geolocation.get_location(ip_address)
            if location:
                last_location_key = f"last_location:{user_id}"
                last_location = self.redis.get(last_location_key)
                
                if last_location:
                    last_location = json.loads(last_location)
                    if (last_location.get('country_code') != location.get('country_code') and
                        self._calculate_distance(last_location, location) > 1000):  # 1000km
                        
                        events.append(SecurityEvent(
                            event_id=self._generate_event_id(),
                            event_type=SecurityEventType.ANOMALOUS_BEHAVIOR,
                            severity=SecurityEventSeverity.MEDIUM,
                            timestamp=current_time,
                            user_id=user_id,
                            ip_address=ip_address,
                            user_agent=user_agent,
                            endpoint='/auth/login',
                            details={
                                'anomaly_type': 'unusual_geolocation',
                                'previous_location': last_location,
                                'current_location': location,
                                'distance_km': self._calculate_distance(last_location, location)
                            },
                            geolocation=location
                        ))
                
                # Update last location
                self.redis.setex(last_location_key, 86400, json.dumps(location))
        
        return events
    
    def analyze_api_request(self, user_id: Optional[str], ip_address: str,
                          endpoint: str, method: str, payload: str,
                          user_agent: str) -> List[SecurityEvent]:
        """Analyze API request for threats"""
        events = []
        current_time = datetime.utcnow()
        
        # Rate limiting check
        rate_key = f"api_rate:{ip_address}:{current_time.strftime('%Y%m%d%H%M')}"
        requests_per_minute = self.redis.incr(rate_key)
        self.redis.expire(rate_key, 60)
        
        if requests_per_minute > self.thresholds['api_requests_per_minute']:
            events.append(SecurityEvent(
                event_id=self._generate_event_id(),
                event_type=SecurityEventType.API_ABUSE,
                severity=SecurityEventSeverity.HIGH,
                timestamp=current_time,
                user_id=user_id,
                ip_address=ip_address,
                user_agent=user_agent,
                endpoint=endpoint,
                details={
                    'requests_per_minute': requests_per_minute,
                    'threshold_exceeded': 'api_requests_per_minute',
                    'method': method
                },
                geolocation=self.geolocation.get_location(ip_address)
            ))
        
        # Injection attack detection
        for attack_type, patterns in self.suspicious_patterns.items():
            for pattern in patterns:
                if re.search(pattern, payload, re.IGNORECASE):
                    events.append(SecurityEvent(
                        event_id=self._generate_event_id(),
                        event_type=SecurityEventType.INJECTION_ATTEMPT,
                        severity=SecurityEventSeverity.HIGH,
                        timestamp=current_time,
                        user_id=user_id,
                        ip_address=ip_address,
                        user_agent=user_agent,
                        endpoint=endpoint,
                        details={
                            'attack_type': attack_type,
                            'pattern_matched': pattern,
                            'payload_sample': payload[:200],  # First 200 chars
                            'method': method
                        },
                        geolocation=self.geolocation.get_location(ip_address)
                    ))
                    break
        
        return events
    
    def analyze_data_access(self, user_id: str, resource: str, action: str,
                          ip_address: str, user_agent: str) -> List[SecurityEvent]:
        """Analyze data access for violations"""
        events = []
        current_time = datetime.utcnow()
        
        # Check for privilege escalation
        if action in ['admin', 'delete', 'modify_user']:
            user_role_key = f"user_role:{user_id}"
            user_role = self.redis.get(user_role_key)
            
            if user_role and user_role.decode() not in ['admin', 'analyst']:
                events.append(SecurityEvent(
                    event_id=self._generate_event_id(),
                    event_type=SecurityEventType.PRIVILEGE_ESCALATION,
                    severity=SecurityEventSeverity.CRITICAL,
                    timestamp=current_time,
                    user_id=user_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    endpoint=resource,
                    details={
                        'attempted_action': action,
                        'user_role': user_role.decode(),
                        'resource': resource
                    },
                    geolocation=self.geolocation.get_location(ip_address)
                ))
        
        return events
    
    def _generate_event_id(self) -> str:
        """Generate unique event ID"""
        timestamp = str(int(time.time() * 1000000))
        return hashlib.sha256(timestamp.encode()).hexdigest()[:16]
    
    def _calculate_distance(self, loc1: Dict[str, str], loc2: Dict[str, str]) -> float:
        """Calculate distance between two locations (simplified)"""
        try:
            lat1, lon1 = float(loc1['latitude']), float(loc1['longitude'])
            lat2, lon2 = float(loc2['latitude']), float(loc2['longitude'])
            
            # Simplified distance calculation (Haversine formula would be more accurate)
            return abs(lat1 - lat2) * 111 + abs(lon1 - lon2) * 111 * 0.7
        except (KeyError, ValueError, TypeError):
            return 0.0

class SecurityEventProcessor:
    """Process and route security events"""
    
    def __init__(self, elasticsearch_client: elasticsearch.Elasticsearch,
                 redis_client: redis.Redis):
        self.es = elasticsearch_client
        self.redis = redis_client
        self.metrics = SecurityMetrics()
        self.alert_handlers = []
    
    def add_alert_handler(self, handler: Callable[[SecurityEvent], None]):
        """Add alert handler for security events"""
        self.alert_handlers.append(handler)
    
    async def process_event(self, event: SecurityEvent):
        """Process security event"""
        # Update metrics
        self.metrics.security_events_total.labels(
            event_type=event.event_type.value,
            severity=event.severity.value
        ).inc()
        
        # Store in Elasticsearch
        await self._store_in_elasticsearch(event)
        
        # Store in Redis for real-time analysis
        await self._store_in_redis(event)
        
        # Trigger alerts for high/critical events
        if event.severity in [SecurityEventSeverity.HIGH, SecurityEventSeverity.CRITICAL]:
            await self._trigger_alerts(event)
        
        logger.info(f"Processed security event: {event.event_id} - {event.event_type.value}")
    
    async def _store_in_elasticsearch(self, event: SecurityEvent):
        """Store event in Elasticsearch"""
        try:
            index_name = f"security-events-{event.timestamp.strftime('%Y.%m')}"
            await self.es.index(
                index=index_name,
                body=event.to_dict()
            )
        except Exception as e:
            logger.error(f"Failed to store event in Elasticsearch: {e}")
    
    async def _store_in_redis(self, event: SecurityEvent):
        """Store event in Redis for real-time analysis"""
        try:
            # Store recent events
            key = f"recent_events:{event.event_type.value}"
            self.redis.lpush(key, json.dumps(event.to_dict()))
            self.redis.ltrim(key, 0, 99)  # Keep last 100 events
            self.redis.expire(key, 3600)  # 1 hour TTL
        except Exception as e:
            logger.error(f"Failed to store event in Redis: {e}")
    
    async def _trigger_alerts(self, event: SecurityEvent):
        """Trigger alerts for security events"""
        for handler in self.alert_handlers:
            try:
                await asyncio.create_task(self._run_handler(handler, event))
            except Exception as e:
                logger.error(f"Alert handler failed: {e}")
    
    async def _run_handler(self, handler: Callable, event: SecurityEvent):
        """Run alert handler"""
        if asyncio.iscoroutinefunction(handler):
            await handler(event)
        else:
            handler(event)

class SecurityAlertManager:
    """Manage security alerts and notifications"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.email_config = config.get('email', {})
        self.slack_config = config.get('slack', {})
        self.webhook_config = config.get('webhook', {})
    
    async def send_email_alert(self, event: SecurityEvent):
        """Send email alert"""
        if not self.email_config.get('enabled'):
            return
        
        try:
            msg = MIMEMultipart()
            msg['From'] = self.email_config['from']
            msg['To'] = ', '.join(self.email_config['to'])
            msg['Subject'] = f"Security Alert: {event.event_type.value} - {event.severity.value}"
            
            body = f"""
            Security Event Detected
            
            Event ID: {event.event_id}
            Type: {event.event_type.value}
            Severity: {event.severity.value}
            Timestamp: {event.timestamp}
            User ID: {event.user_id}
            IP Address: {event.ip_address}
            Endpoint: {event.endpoint}
            
            Details:
            {json.dumps(event.details, indent=2)}
            
            Geolocation: {event.geolocation}
            """
            
            msg.attach(MIMEText(body, 'plain'))
            
            server = smtplib.SMTP(self.email_config['smtp_host'], self.email_config['smtp_port'])
            if self.email_config.get('use_tls'):
                server.starttls()
            if self.email_config.get('username'):
                server.login(self.email_config['username'], self.email_config['password'])
            
            server.send_message(msg)
            server.quit()
            
            logger.info(f"Email alert sent for event {event.event_id}")
        except Exception as e:
            logger.error(f"Failed to send email alert: {e}")
    
    async def send_slack_alert(self, event: SecurityEvent):
        """Send Slack alert"""
        if not self.slack_config.get('enabled'):
            return
        
        try:
            webhook_url = self.slack_config['webhook_url']
            
            color = {
                SecurityEventSeverity.LOW: 'good',
                SecurityEventSeverity.MEDIUM: 'warning',
                SecurityEventSeverity.HIGH: 'danger',
                SecurityEventSeverity.CRITICAL: 'danger'
            }.get(event.severity, 'warning')
            
            payload = {
                'attachments': [{
                    'color': color,
                    'title': f'Security Alert: {event.event_type.value}',
                    'fields': [
                        {'title': 'Severity', 'value': event.severity.value, 'short': True},
                        {'title': 'User ID', 'value': event.user_id or 'N/A', 'short': True},
                        {'title': 'IP Address', 'value': event.ip_address, 'short': True},
                        {'title': 'Timestamp', 'value': event.timestamp.isoformat(), 'short': True},
                    ],
                    'text': json.dumps(event.details, indent=2)
                }]
            }
            
            response = requests.post(webhook_url, json=payload)
            response.raise_for_status()
            
            logger.info(f"Slack alert sent for event {event.event_id}")
        except Exception as e:
            logger.error(f"Failed to send Slack alert: {e}")

# Factory function
def create_security_monitor(config: Dict[str, Any]) -> ThreatDetector:
    """Create security monitoring system"""
    redis_client = redis.Redis(
        host=config.get('redis_host', 'localhost'),
        port=config.get('redis_port', 6379),
        db=config.get('redis_db', 0)
    )
    
    return ThreatDetector(redis_client)
