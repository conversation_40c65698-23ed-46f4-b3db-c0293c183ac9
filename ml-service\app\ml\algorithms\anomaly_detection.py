"""
Anomaly detection models for fraud detection
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
from sklearn.ensemble import IsolationForest
from sklearn.svm import OneClassSVM
from sklearn.neighbors import LocalOutlierFactor
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import DBSCAN
from sklearn.covariance import EllipticEnvelope
import joblib
import mlflow
import mlflow.sklearn

from ...core.config import settings
from ...core.logging import get_logger
from ...core.exceptions import ModelTrainingException, ModelLoadException

logger = get_logger(__name__)


class AnomalyDetectionEnsemble:
    """Ensemble of anomaly detection models for fraud detection"""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.thresholds = {}
        self.feature_names = None
        self.is_trained = False
        self.model_weights = None
    
    def create_isolation_forest(self, **kwargs) -> IsolationForest:
        """Create Isolation Forest model"""
        default_params = {
            'n_estimators': settings.ANOMALY_N_ESTIMATORS,
            'contamination': settings.ANOMALY_CONTAMINATION,
            'max_samples': 'auto',
            'max_features': 1.0,
            'bootstrap': False,
            'random_state': 42,
            'n_jobs': -1
        }
        default_params.update(kwargs)
        return IsolationForest(**default_params)
    
    def create_one_class_svm(self, **kwargs) -> OneClassSVM:
        """Create One-Class SVM model"""
        default_params = {
            'kernel': 'rbf',
            'gamma': 'scale',
            'nu': settings.ANOMALY_CONTAMINATION,
            'shrinking': True,
            'cache_size': 200
        }
        default_params.update(kwargs)
        return OneClassSVM(**default_params)
    
    def create_local_outlier_factor(self, **kwargs) -> LocalOutlierFactor:
        """Create Local Outlier Factor model"""
        default_params = {
            'n_neighbors': 20,
            'algorithm': 'auto',
            'leaf_size': 30,
            'metric': 'minkowski',
            'p': 2,
            'contamination': settings.ANOMALY_CONTAMINATION,
            'novelty': True,  # For prediction on new data
            'n_jobs': -1
        }
        default_params.update(kwargs)
        return LocalOutlierFactor(**default_params)
    
    def create_elliptic_envelope(self, **kwargs) -> EllipticEnvelope:
        """Create Elliptic Envelope model"""
        default_params = {
            'contamination': settings.ANOMALY_CONTAMINATION,
            'support_fraction': None,
            'random_state': 42
        }
        default_params.update(kwargs)
        return EllipticEnvelope(**default_params)
    
    def create_dbscan_anomaly_detector(self, **kwargs):
        """Create DBSCAN-based anomaly detector"""
        default_params = {
            'eps': 0.5,
            'min_samples': 5,
            'metric': 'euclidean',
            'algorithm': 'auto',
            'leaf_size': 30,
            'n_jobs': -1
        }
        default_params.update(kwargs)
        return DBSCAN(**default_params)
    
    def train_ensemble(
        self,
        X_train: pd.DataFrame,
        y_train: Optional[pd.Series] = None,
        model_types: List[str] = None
    ) -> Dict[str, Any]:
        """Train ensemble of anomaly detection models"""
        
        if model_types is None:
            model_types = ['isolation_forest', 'one_class_svm', 'local_outlier_factor', 'elliptic_envelope']
        
        logger.info("Starting anomaly detection ensemble training", model_types=model_types)
        
        # Store feature names
        self.feature_names = list(X_train.columns)
        
        # Filter to normal transactions only for unsupervised training
        if y_train is not None:
            X_normal = X_train[y_train == 0]
            logger.info(f"Training on {len(X_normal)} normal transactions out of {len(X_train)} total")
        else:
            X_normal = X_train
            logger.info(f"Training on all {len(X_train)} transactions (unsupervised)")
        
        model_creators = {
            'isolation_forest': self.create_isolation_forest,
            'one_class_svm': self.create_one_class_svm,
            'local_outlier_factor': self.create_local_outlier_factor,
            'elliptic_envelope': self.create_elliptic_envelope
        }
        
        training_results = {}
        
        with mlflow.start_run(run_name="anomaly_detection_ensemble"):
            mlflow.log_param("model_types", model_types)
            mlflow.log_param("training_samples", len(X_normal))
            mlflow.log_param("feature_count", len(self.feature_names))
            mlflow.log_param("contamination", settings.ANOMALY_CONTAMINATION)
            
            for model_name in model_types:
                if model_name not in model_creators:
                    logger.warning(f"Unknown anomaly detection model: {model_name}")
                    continue
                
                logger.info(f"Training {model_name}")
                
                try:
                    # Create scaler for this model
                    scaler = StandardScaler()
                    X_scaled = scaler.fit_transform(X_normal)
                    
                    # Create and train model
                    model = model_creators[model_name]()
                    
                    if model_name == 'dbscan':
                        # DBSCAN doesn't have fit/predict_proba, handle separately
                        labels = model.fit_predict(X_scaled)
                        # Consider points in small clusters or noise as anomalies
                        unique_labels, counts = np.unique(labels, return_counts=True)
                        small_clusters = unique_labels[counts < 10]  # Clusters with < 10 points
                        anomaly_threshold = -1  # Noise points
                        
                        self.models[model_name] = model
                        self.scalers[model_name] = scaler
                        self.thresholds[model_name] = {'small_clusters': small_clusters, 'noise_label': -1}
                    else:
                        # Standard anomaly detection models
                        model.fit(X_scaled)
                        
                        # Calculate anomaly scores on training data
                        if hasattr(model, 'decision_function'):
                            scores = model.decision_function(X_scaled)
                        elif hasattr(model, 'score_samples'):
                            scores = model.score_samples(X_scaled)
                        else:
                            scores = None
                        
                        self.models[model_name] = model
                        self.scalers[model_name] = scaler
                        
                        if scores is not None:
                            # Set threshold for anomaly detection
                            threshold = np.percentile(scores, settings.ANOMALY_CONTAMINATION * 100)
                            self.thresholds[model_name] = threshold
                    
                    # Evaluate on full dataset if labels available
                    if y_train is not None:
                        X_full_scaled = scaler.transform(X_train)
                        predictions = self._predict_single_model(model_name, X_full_scaled)
                        
                        # Calculate metrics
                        from sklearn.metrics import precision_score, recall_score, f1_score
                        precision = precision_score(y_train, predictions)
                        recall = recall_score(y_train, predictions)
                        f1 = f1_score(y_train, predictions)
                        
                        training_results[model_name] = {
                            'precision': precision,
                            'recall': recall,
                            'f1_score': f1
                        }
                        
                        # Log to MLflow
                        with mlflow.start_run(run_name=f"{model_name}_anomaly", nested=True):
                            mlflow.log_param("model_type", model_name)
                            mlflow.log_metric("precision", precision)
                            mlflow.log_metric("recall", recall)
                            mlflow.log_metric("f1_score", f1)
                            mlflow.sklearn.log_model(model, f"{model_name}_model")
                    
                    logger.info(f"{model_name} training completed")
                    
                except Exception as e:
                    logger.error(f"Failed to train {model_name}", error=str(e))
                    continue
            
            # Calculate ensemble weights based on F1 scores
            if training_results:
                self._calculate_ensemble_weights(training_results)
                mlflow.log_param("ensemble_weights", self.model_weights)
        
        self.is_trained = True
        logger.info("Anomaly detection ensemble training completed", results=training_results)
        
        return training_results
    
    def _calculate_ensemble_weights(self, training_results: Dict[str, Any]):
        """Calculate ensemble weights based on model performance"""
        f1_scores = {name: results['f1_score'] for name, results in training_results.items()}
        total_f1 = sum(f1_scores.values())
        
        if total_f1 > 0:
            self.model_weights = {name: score / total_f1 for name, score in f1_scores.items()}
        else:
            # Equal weights if no valid scores
            self.model_weights = {name: 1.0 / len(self.models) for name in self.models.keys()}
        
        logger.info("Anomaly detection ensemble weights calculated", weights=self.model_weights)
    
    def _predict_single_model(self, model_name: str, X_scaled: np.ndarray) -> np.ndarray:
        """Make predictions with a single model"""
        model = self.models[model_name]
        
        if model_name == 'dbscan':
            # DBSCAN prediction logic
            labels = model.fit_predict(X_scaled)  # DBSCAN doesn't have predict method
            threshold_info = self.thresholds[model_name]
            anomalies = np.isin(labels, threshold_info['small_clusters']) | (labels == threshold_info['noise_label'])
            return anomalies.astype(int)
        else:
            # Standard anomaly detection
            predictions = model.predict(X_scaled)
            # Convert sklearn anomaly predictions (-1 for anomaly, 1 for normal) to binary (1 for anomaly, 0 for normal)
            return (predictions == -1).astype(int)
    
    def predict_proba(self, X: pd.DataFrame) -> np.ndarray:
        """Make ensemble anomaly predictions with probabilities"""
        if not self.is_trained or not self.models:
            raise ModelLoadException("Anomaly detection models not trained or loaded")
        
        predictions = []
        weights = []
        
        for model_name, model in self.models.items():
            try:
                scaler = self.scalers[model_name]
                X_scaled = scaler.transform(X)
                
                if hasattr(model, 'decision_function'):
                    # Get decision function scores and convert to probabilities
                    scores = model.decision_function(X_scaled)
                    # Normalize scores to [0, 1] range
                    min_score, max_score = scores.min(), scores.max()
                    if max_score > min_score:
                        probabilities = (scores - min_score) / (max_score - min_score)
                    else:
                        probabilities = np.ones_like(scores) * 0.5
                elif hasattr(model, 'score_samples'):
                    # Get likelihood scores and convert to anomaly probabilities
                    scores = model.score_samples(X_scaled)
                    # Higher likelihood = lower anomaly probability
                    probabilities = 1 / (1 + np.exp(scores))  # Sigmoid transformation
                else:
                    # Binary predictions only
                    binary_pred = self._predict_single_model(model_name, X_scaled)
                    probabilities = binary_pred.astype(float)
                
                predictions.append(probabilities)
                weights.append(self.model_weights.get(model_name, 1.0))
                
            except Exception as e:
                logger.warning(f"Failed to get prediction from {model_name}", error=str(e))
        
        if not predictions:
            raise ModelLoadException("No anomaly detection models available for prediction")
        
        # Weighted average of predictions
        predictions = np.array(predictions)
        weights = np.array(weights)
        weights = weights / weights.sum()  # Normalize weights
        
        ensemble_pred = np.average(predictions, axis=0, weights=weights)
        
        return ensemble_pred
    
    def predict(self, X: pd.DataFrame, threshold: float = 0.5) -> np.ndarray:
        """Make binary anomaly predictions"""
        probabilities = self.predict_proba(X)
        return (probabilities >= threshold).astype(int)
    
    def get_anomaly_scores(self, X: pd.DataFrame) -> Dict[str, np.ndarray]:
        """Get anomaly scores from individual models"""
        if not self.is_trained:
            raise ModelLoadException("Models not trained")
        
        scores = {}
        
        for model_name, model in self.models.items():
            try:
                scaler = self.scalers[model_name]
                X_scaled = scaler.transform(X)
                
                if hasattr(model, 'decision_function'):
                    scores[model_name] = model.decision_function(X_scaled)
                elif hasattr(model, 'score_samples'):
                    scores[model_name] = model.score_samples(X_scaled)
                else:
                    # Binary predictions
                    scores[model_name] = self._predict_single_model(model_name, X_scaled).astype(float)
                    
            except Exception as e:
                logger.warning(f"Failed to get scores from {model_name}", error=str(e))
        
        return scores
    
    def save_models(self, model_dir: str):
        """Save anomaly detection models"""
        import os
        os.makedirs(model_dir, exist_ok=True)
        
        # Save models and scalers
        for model_name, model in self.models.items():
            model_path = os.path.join(model_dir, f"{model_name}_anomaly.pkl")
            scaler_path = os.path.join(model_dir, f"{model_name}_scaler.pkl")
            
            joblib.dump(model, model_path)
            joblib.dump(self.scalers[model_name], scaler_path)
        
        # Save metadata
        metadata = {
            'model_weights': self.model_weights,
            'thresholds': self.thresholds,
            'feature_names': self.feature_names,
            'is_trained': self.is_trained
        }
        metadata_path = os.path.join(model_dir, "anomaly_metadata.pkl")
        joblib.dump(metadata, metadata_path)
        
        logger.info("Anomaly detection models saved", model_dir=model_dir)
    
    def load_models(self, model_dir: str):
        """Load anomaly detection models"""
        import os
        
        # Load metadata
        metadata_path = os.path.join(model_dir, "anomaly_metadata.pkl")
        if os.path.exists(metadata_path):
            metadata = joblib.load(metadata_path)
            self.model_weights = metadata['model_weights']
            self.thresholds = metadata['thresholds']
            self.feature_names = metadata['feature_names']
            self.is_trained = metadata['is_trained']
        
        # Load models and scalers
        self.models = {}
        self.scalers = {}
        
        for file_name in os.listdir(model_dir):
            if file_name.endswith('_anomaly.pkl'):
                model_name = file_name.replace('_anomaly.pkl', '')
                model_path = os.path.join(model_dir, file_name)
                scaler_path = os.path.join(model_dir, f"{model_name}_scaler.pkl")
                
                if os.path.exists(scaler_path):
                    self.models[model_name] = joblib.load(model_path)
                    self.scalers[model_name] = joblib.load(scaler_path)
        
        logger.info("Anomaly detection models loaded", model_count=len(self.models))
