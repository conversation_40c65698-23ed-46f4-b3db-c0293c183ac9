# FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Data validation and serialization
pydantic==2.5.0
pydantic-settings==2.1.0

# Database
sqlalchemy[asyncio]==2.0.23
alembic==1.13.0
asyncpg==0.29.0
psycopg2-binary==2.9.9

# Caching and Redis
redis==5.0.1
hiredis==2.2.3

# Message queue
kafka-python==2.0.2
aiokafka==0.9.0

# Data processing and ML
pandas==2.1.4
numpy==1.25.2
scikit-learn==1.3.2
joblib==1.3.2

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
bcrypt==4.1.2
PyJWT==2.8.0

# HTTP client and async
aiofiles==23.2.1
httpx==0.25.2
aiohttp==3.9.1

# Email notifications
aiosmtplib==3.0.1

# Monitoring and logging
prometheus-client==0.19.0
structlog==23.2.0
python-json-logger==2.0.7

# Background tasks and utilities
tenacity==8.2.3
celery==5.3.4
flower==2.0.1
python-dateutil==2.8.2
orjson==3.9.10
slowapi==0.1.9

# Environment and configuration
python-dotenv==1.0.0

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# CLI utilities
click==8.1.7
rich==13.7.0
typer==0.9.0
