"""
Fraud Detection Service for handling alerts and notifications
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime

from app.core.config import settings
from app.core.logging import get_logger, log_alert_sent
from app.schemas.transaction import TransactionResponse, FraudResult
from app.services.notification_service import NotificationService
from app.services.message_queue_service import message_queue_service

logger = get_logger(__name__)


class FraudDetectionService:
    """Service for fraud detection logic and alert management"""
    
    def __init__(self):
        self.notification_service = NotificationService()
    
    async def send_alerts_if_needed(self, transaction: TransactionResponse):
        """
        Send alerts if transaction meets criteria for notification
        
        Args:
            transaction: Processed transaction with fraud results
        """
        try:
            if not transaction.fraud_result:
                return
            
            fraud_result = transaction.fraud_result
            
            # Determine if alerts should be sent
            should_alert = self._should_send_alert(fraud_result)
            
            if should_alert:
                await self._send_fraud_alerts(transaction, fraud_result)
                
        except Exception as e:
            logger.error(
                "Failed to process fraud alerts",
                transaction_id=transaction.transaction_id,
                error=str(e)
            )
    
    def _should_send_alert(self, fraud_result: FraudResult) -> bool:
        """
        Determine if an alert should be sent based on fraud results
        
        Args:
            fraud_result: Fraud detection results
            
        Returns:
            True if alert should be sent
        """
        # Send alerts for high-risk transactions
        if fraud_result.fraud_score >= settings.FRAUD_SCORE_THRESHOLD_HIGH:
            return True
        
        # Send alerts for blocked transactions
        if fraud_result.decision.value == "BLOCK":
            return True
        
        # Send alerts for specific fraud patterns
        high_risk_reasons = [
            "velocity_anomaly",
            "suspicious_location",
            "account_takeover",
            "money_laundering_pattern"
        ]
        
        if any(reason in fraud_result.reasons for reason in high_risk_reasons):
            return True
        
        return False
    
    async def _send_fraud_alerts(
        self,
        transaction: TransactionResponse,
        fraud_result: FraudResult
    ):
        """
        Send fraud alerts through various channels
        
        Args:
            transaction: Transaction data
            fraud_result: Fraud detection results
        """
        try:
            # Prepare alert data
            alert_data = {
                "alert_id": f"alert_{transaction.transaction_id}_{int(datetime.utcnow().timestamp())}",
                "transaction_id": transaction.transaction_id,
                "alert_type": "fraud_detection",
                "severity": self._determine_alert_severity(fraud_result),
                "fraud_score": fraud_result.fraud_score,
                "risk_level": fraud_result.risk_level,
                "decision": fraud_result.decision.value,
                "reasons": fraud_result.reasons,
                "transaction_amount": float(transaction.amount),
                "transaction_type": transaction.type.value,
                "originator_account": transaction.originator.account_id,
                "beneficiary_account": transaction.beneficiary.account_id,
                "timestamp": datetime.utcnow().isoformat(),
                "requires_immediate_attention": fraud_result.fraud_score >= 0.9
            }
            
            # Send to message queue for async processing
            await message_queue_service.publish_alert(alert_data)
            
            # Send immediate notifications for high-severity alerts
            if alert_data["severity"] in ["HIGH", "CRITICAL"]:
                await self._send_immediate_notifications(alert_data)
            
            logger.info(
                "Fraud alerts sent",
                transaction_id=transaction.transaction_id,
                alert_id=alert_data["alert_id"],
                severity=alert_data["severity"]
            )
            
        except Exception as e:
            logger.error(
                "Failed to send fraud alerts",
                transaction_id=transaction.transaction_id,
                error=str(e)
            )
    
    def _determine_alert_severity(self, fraud_result: FraudResult) -> str:
        """
        Determine alert severity based on fraud results
        
        Args:
            fraud_result: Fraud detection results
            
        Returns:
            Alert severity level
        """
        if fraud_result.fraud_score >= 0.9:
            return "CRITICAL"
        elif fraud_result.fraud_score >= 0.8:
            return "HIGH"
        elif fraud_result.fraud_score >= 0.6:
            return "MEDIUM"
        else:
            return "LOW"
    
    async def _send_immediate_notifications(self, alert_data: Dict[str, Any]):
        """
        Send immediate notifications for high-severity alerts
        
        Args:
            alert_data: Alert information
        """
        try:
            # Prepare notification message
            message = self._format_alert_message(alert_data)
            
            # Send Slack notification if configured
            if settings.SLACK_WEBHOOK_URL:
                await self.notification_service.send_slack_alert(
                    message=message,
                    alert_data=alert_data
                )
                
                log_alert_sent(
                    logger,
                    "fraud_alert",
                    alert_data["transaction_id"],
                    "fraud_ops_team",
                    "slack"
                )
            
            # Send email notification if configured
            if settings.SMTP_HOST:
                await self.notification_service.send_email_alert(
                    subject=f"FRAUD ALERT - {alert_data['severity']} Risk Transaction",
                    message=message,
                    alert_data=alert_data
                )
                
                log_alert_sent(
                    logger,
                    "fraud_alert",
                    alert_data["transaction_id"],
                    "fraud_ops_team",
                    "email"
                )
            
        except Exception as e:
            logger.error(
                "Failed to send immediate notifications",
                alert_id=alert_data.get("alert_id"),
                error=str(e)
            )
    
    def _format_alert_message(self, alert_data: Dict[str, Any]) -> str:
        """
        Format alert message for notifications
        
        Args:
            alert_data: Alert information
            
        Returns:
            Formatted message string
        """
        return f"""
🚨 FRAUD ALERT - {alert_data['severity']} RISK

Transaction ID: {alert_data['transaction_id']}
Fraud Score: {alert_data['fraud_score']:.3f}
Risk Level: {alert_data['risk_level']}
Decision: {alert_data['decision']}

Transaction Details:
- Amount: ${alert_data['transaction_amount']:,.2f}
- Type: {alert_data['transaction_type']}
- From: {alert_data['originator_account']}
- To: {alert_data['beneficiary_account']}

Fraud Indicators:
{chr(10).join(f"• {reason}" for reason in alert_data['reasons'])}

Time: {alert_data['timestamp']}
Alert ID: {alert_data['alert_id']}

{"⚠️ REQUIRES IMMEDIATE ATTENTION" if alert_data.get('requires_immediate_attention') else ""}
        """.strip()
    
    async def process_alert_feedback(
        self,
        transaction_id: str,
        is_actual_fraud: bool,
        feedback_notes: Optional[str] = None
    ):
        """
        Process feedback on fraud alerts for model improvement
        
        Args:
            transaction_id: Transaction identifier
            is_actual_fraud: Whether the transaction was actually fraudulent
            feedback_notes: Additional feedback notes
        """
        try:
            # This would typically update the ML model with feedback
            feedback_data = {
                "transaction_id": transaction_id,
                "actual_fraud": is_actual_fraud,
                "feedback_notes": feedback_notes,
                "feedback_timestamp": datetime.utcnow().isoformat(),
                "feedback_type": "manual_review"
            }
            
            # Send feedback to ML service for model improvement
            from app.services.ml_service import MLService
            ml_service = MLService()
            
            # This would require the original prediction score
            # In a real implementation, you'd retrieve this from the database
            await ml_service.update_model_feedback(
                transaction_id=transaction_id,
                actual_fraud=is_actual_fraud,
                predicted_score=0.0  # Would need to retrieve actual score
            )
            
            logger.info(
                "Alert feedback processed",
                transaction_id=transaction_id,
                actual_fraud=is_actual_fraud
            )
            
        except Exception as e:
            logger.error(
                "Failed to process alert feedback",
                transaction_id=transaction_id,
                error=str(e)
            )
    
    async def get_fraud_patterns(self, days: int = 30) -> List[Dict[str, Any]]:
        """
        Analyze fraud patterns from recent alerts
        
        Args:
            days: Number of days to analyze
            
        Returns:
            List of identified fraud patterns
        """
        try:
            # This would analyze recent fraud cases to identify patterns
            # For now, return mock patterns
            patterns = [
                {
                    "pattern_type": "velocity_anomaly",
                    "frequency": 15,
                    "avg_amount": 5000.0,
                    "description": "Multiple high-value transactions in short time"
                },
                {
                    "pattern_type": "geographic_anomaly",
                    "frequency": 8,
                    "avg_amount": 2500.0,
                    "description": "Transactions from unusual locations"
                },
                {
                    "pattern_type": "account_takeover",
                    "frequency": 5,
                    "avg_amount": 10000.0,
                    "description": "Sudden change in transaction patterns"
                }
            ]
            
            return patterns
            
        except Exception as e:
            logger.error(
                "Failed to get fraud patterns",
                error=str(e)
            )
            return []
