@echo off
echo === FraudShield Phase 8 Setup ===
echo Setting up deployment, monitoring, and go-live infrastructure...

REM Create necessary directories
if not exist "monitoring\prometheus" mkdir "monitoring\prometheus"
if not exist "monitoring\grafana\dashboards" mkdir "monitoring\grafana\dashboards"
if not exist "monitoring\grafana\datasources" mkdir "monitoring\grafana\datasources"
if not exist "monitoring\alertmanager" mkdir "monitoring\alertmanager"
if not exist "monitoring\logstash\pipeline" mkdir "monitoring\logstash\pipeline"
if not exist "monitoring\logstash\config" mkdir "monitoring\logstash\config"
if not exist "monitoring\logs" mkdir "monitoring\logs"
if not exist "secrets" mkdir "secrets"
if not exist "backup" mkdir "backup"

echo.
echo Checking Phase 8 components...

REM Check if all components exist
set "allReady=true"

if exist "docker-compose.prod.yml" (
    echo [32m✓ Production Docker Compose[0m
) else (
    echo [31m✗ Production Docker Compose - Missing[0m
    set "allReady=false"
)

if exist "monitoring\prometheus\prometheus.yml" (
    echo [32m✓ Prometheus Config[0m
) else (
    echo [31m✗ Prometheus Config - Missing[0m
    set "allReady=false"
)

if exist "monitoring\prometheus\alert_rules.yml" (
    echo [32m✓ Alert Rules[0m
) else (
    echo [31m✗ Alert Rules - Missing[0m
    set "allReady=false"
)

if exist "monitoring\grafana\datasources\datasources.yml" (
    echo [32m✓ Grafana Datasources[0m
) else (
    echo [31m✗ Grafana Datasources - Missing[0m
    set "allReady=false"
)

if exist "monitoring\alertmanager\alertmanager.yml" (
    echo [32m✓ Alertmanager Config[0m
) else (
    echo [31m✗ Alertmanager Config - Missing[0m
    set "allReady=false"
)

if exist "scripts\deployment\health-check.sh" (
    echo [32m✓ Health Check Script[0m
) else (
    echo [31m✗ Health Check Script - Missing[0m
    set "allReady=false"
)

if exist "scripts\deployment\deploy-production.sh" (
    echo [32m✓ Deployment Script[0m
) else (
    echo [31m✗ Deployment Script - Missing[0m
    set "allReady=false"
)

if exist "scripts\go-live.sh" (
    echo [32m✓ Go-Live Script[0m
) else (
    echo [31m✗ Go-Live Script - Missing[0m
    set "allReady=false"
)

if exist "docs\PHASE8_GO_LIVE_CHECKLIST.md" (
    echo [32m✓ Go-Live Checklist[0m
) else (
    echo [31m✗ Go-Live Checklist - Missing[0m
    set "allReady=false"
)

if exist "docs\PHASE8_COMPLETION.md" (
    echo [32m✓ Phase 8 Documentation[0m
) else (
    echo [31m✗ Phase 8 Documentation - Missing[0m
    set "allReady=false"
)

echo.
if "%allReady%"=="true" (
    echo [32m=== Phase 8 Setup Complete! ===[0m
    echo [32mAll components are ready for production deployment.[0m
    echo.
    echo [33mNext Steps:[0m
    echo 1. Review: docs\PHASE8_GO_LIVE_CHECKLIST.md
    echo 2. Setup secrets: bash scripts/deployment/setup-secrets.sh
    echo 3. Deploy: bash scripts/go-live.sh production latest
    echo.
    echo [36mAccess Points (after deployment):[0m
    echo • Frontend: http://localhost:3000
    echo • API: http://localhost:8000/docs
    echo • Monitoring: http://localhost:3001
    echo • Metrics: http://localhost:9090
    echo.
    echo [35m=== FraudShield Phase 8 Ready! ===[0m
) else (
    echo [31mSome components are missing. Please check the files above.[0m
    exit /b 1
)

pause
