#!/usr/bin/env python3
"""
Automated Model Training Pipeline
Handles data loading, preprocessing, training, and model registration
"""

import os
import sys
import argparse
import logging
import json
import pickle
from datetime import datetime
from typing import Dict, Any, Tuple, Optional
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
import mlflow
import mlflow.sklearn
from mlflow.tracking import MlflowClient
import optuna
from optuna.integration.mlflow import MLflow<PERSON>allback
import great_expectations as ge
from great_expectations.core import ExpectationSuite
import joblib

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ml_service.src.feature_engineering import FeatureEngineer
from ml_service.src.data_preprocessing import DataPreprocessor
from ml_service.src.model_evaluation import ModelEvaluator

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelTrainer:
    """Automated model training with MLOps best practices"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.mlflow_client = MlflowClient()
        self.feature_engineer = FeatureEngineer()
        self.data_preprocessor = DataPreprocessor()
        self.model_evaluator = ModelEvaluator()
        
        # Model configurations
        self.model_configs = {
            'random_forest': {
                'model': RandomForestClassifier,
                'params': {
                    'n_estimators': [100, 200, 300],
                    'max_depth': [10, 20, None],
                    'min_samples_split': [2, 5, 10],
                    'min_samples_leaf': [1, 2, 4],
                    'random_state': [42]
                }
            },
            'gradient_boosting': {
                'model': GradientBoostingClassifier,
                'params': {
                    'n_estimators': [100, 200],
                    'learning_rate': [0.01, 0.1, 0.2],
                    'max_depth': [3, 5, 7],
                    'random_state': [42]
                }
            },
            'logistic_regression': {
                'model': LogisticRegression,
                'params': {
                    'C': [0.1, 1.0, 10.0],
                    'penalty': ['l1', 'l2'],
                    'solver': ['liblinear', 'saga'],
                    'random_state': [42]
                }
            }
        }
    
    def load_and_validate_data(self) -> pd.DataFrame:
        """Load and validate training data"""
        logger.info("Loading training data...")
        
        # Load data from configured source
        data_path = self.config.get('data_path', 'data/processed/training_data.csv')
        
        if not os.path.exists(data_path):
            raise FileNotFoundError(f"Training data not found at {data_path}")
        
        df = pd.read_csv(data_path)
        logger.info(f"Loaded {len(df)} records")
        
        # Data validation with Great Expectations
        logger.info("Validating data quality...")
        ge_df = ge.from_pandas(df)
        
        # Define expectations
        suite = ExpectationSuite(expectation_suite_name="training_data_validation")
        
        # Basic data quality checks
        ge_df.expect_table_row_count_to_be_between(min_value=1000, max_value=None)
        ge_df.expect_column_to_exist("isFraud")
        ge_df.expect_column_values_to_be_in_set("isFraud", [0, 1])
        ge_df.expect_column_values_to_not_be_null("amount")
        ge_df.expect_column_values_to_be_between("amount", min_value=0, max_value=None)
        
        # Validate expectations
        validation_result = ge_df.validate(expectation_suite=suite)
        
        if not validation_result.success:
            logger.error("Data validation failed!")
            for result in validation_result.results:
                if not result.success:
                    logger.error(f"Failed expectation: {result.expectation_config.expectation_type}")
            raise ValueError("Data validation failed")
        
        logger.info("Data validation passed")
        return df
    
    def prepare_features(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.Series]:
        """Prepare features for training"""
        logger.info("Engineering features...")
        
        # Feature engineering
        df_features = self.feature_engineer.create_features(df)
        
        # Data preprocessing
        df_processed = self.data_preprocessor.preprocess(df_features)
        
        # Separate features and target
        target_column = 'isFraud'
        if target_column not in df_processed.columns:
            raise ValueError(f"Target column '{target_column}' not found in data")
        
        X = df_processed.drop(columns=[target_column])
        y = df_processed[target_column]
        
        logger.info(f"Prepared {X.shape[1]} features for {len(X)} samples")
        logger.info(f"Class distribution: {y.value_counts().to_dict()}")
        
        return X, y
    
    def optimize_hyperparameters(self, X_train: pd.DataFrame, y_train: pd.Series, 
                                model_name: str) -> Dict[str, Any]:
        """Optimize hyperparameters using Optuna"""
        logger.info(f"Optimizing hyperparameters for {model_name}...")
        
        model_config = self.model_configs[model_name]
        
        def objective(trial):
            # Sample hyperparameters
            params = {}
            for param, values in model_config['params'].items():
                if param == 'random_state':
                    params[param] = values[0]
                elif isinstance(values[0], int):
                    params[param] = trial.suggest_int(param, min(values), max(values))
                elif isinstance(values[0], float):
                    params[param] = trial.suggest_float(param, min(values), max(values))
                else:
                    params[param] = trial.suggest_categorical(param, values)
            
            # Train model with sampled parameters
            model = model_config['model'](**params)
            
            # Cross-validation score
            cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='roc_auc')
            return cv_scores.mean()
        
        # Create study
        study = optuna.create_study(direction='maximize')
        
        # MLflow callback for tracking
        mlflc = MLflowCallback(
            tracking_uri=mlflow.get_tracking_uri(),
            metric_name='roc_auc'
        )
        
        # Optimize
        study.optimize(objective, n_trials=50, callbacks=[mlflc])
        
        logger.info(f"Best parameters: {study.best_params}")
        logger.info(f"Best score: {study.best_value:.4f}")
        
        return study.best_params
    
    def train_model(self, X_train: pd.DataFrame, y_train: pd.Series, 
                   model_name: str, params: Dict[str, Any]) -> Any:
        """Train model with given parameters"""
        logger.info(f"Training {model_name} model...")
        
        model_config = self.model_configs[model_name]
        model = model_config['model'](**params)
        
        # Train model
        model.fit(X_train, y_train)
        
        logger.info("Model training completed")
        return model
    
    def evaluate_model(self, model: Any, X_test: pd.DataFrame, y_test: pd.Series) -> Dict[str, Any]:
        """Evaluate trained model"""
        logger.info("Evaluating model...")
        
        # Predictions
        y_pred = model.predict(X_test)
        y_pred_proba = model.predict_proba(X_test)[:, 1]
        
        # Metrics
        metrics = self.model_evaluator.calculate_metrics(y_test, y_pred, y_pred_proba)
        
        # Classification report
        class_report = classification_report(y_test, y_pred, output_dict=True)
        
        # Confusion matrix
        conf_matrix = confusion_matrix(y_test, y_pred)
        
        evaluation_results = {
            'metrics': metrics,
            'classification_report': class_report,
            'confusion_matrix': conf_matrix.tolist(),
            'feature_importance': None
        }
        
        # Feature importance (if available)
        if hasattr(model, 'feature_importances_'):
            feature_names = X_test.columns.tolist()
            feature_importance = dict(zip(feature_names, model.feature_importances_))
            evaluation_results['feature_importance'] = feature_importance
        
        logger.info(f"Model evaluation completed. ROC-AUC: {metrics['roc_auc']:.4f}")
        return evaluation_results
    
    def log_to_mlflow(self, model: Any, params: Dict[str, Any], 
                     metrics: Dict[str, Any], artifacts: Dict[str, Any]) -> str:
        """Log model and metrics to MLflow"""
        logger.info("Logging to MLflow...")
        
        with mlflow.start_run() as run:
            # Log parameters
            mlflow.log_params(params)
            
            # Log metrics
            mlflow.log_metrics(metrics['metrics'])
            
            # Log model
            mlflow.sklearn.log_model(
                model, 
                "model",
                registered_model_name=self.config.get('model_name', 'fraud-detection')
            )
            
            # Log artifacts
            for name, artifact in artifacts.items():
                if isinstance(artifact, dict):
                    with open(f"{name}.json", 'w') as f:
                        json.dump(artifact, f, indent=2)
                    mlflow.log_artifact(f"{name}.json")
                elif isinstance(artifact, np.ndarray):
                    np.save(f"{name}.npy", artifact)
                    mlflow.log_artifact(f"{name}.npy")
            
            # Log feature importance plot if available
            if metrics.get('feature_importance'):
                self.model_evaluator.plot_feature_importance(
                    metrics['feature_importance'], 
                    save_path='feature_importance.png'
                )
                mlflow.log_artifact('feature_importance.png')
            
            run_id = run.info.run_id
            logger.info(f"Logged to MLflow with run ID: {run_id}")
            
        return run_id
    
    def save_model_artifacts(self, model: Any, preprocessor: Any, 
                           feature_engineer: Any, run_id: str):
        """Save model artifacts for deployment"""
        logger.info("Saving model artifacts...")
        
        artifacts_dir = f"artifacts/{run_id}"
        os.makedirs(artifacts_dir, exist_ok=True)
        
        # Save model
        joblib.dump(model, f"{artifacts_dir}/model.pkl")
        
        # Save preprocessor
        joblib.dump(preprocessor, f"{artifacts_dir}/preprocessor.pkl")
        
        # Save feature engineer
        joblib.dump(feature_engineer, f"{artifacts_dir}/feature_engineer.pkl")
        
        # Save metadata
        metadata = {
            'run_id': run_id,
            'model_type': type(model).__name__,
            'created_at': datetime.utcnow().isoformat(),
            'features': self.feature_engineer.get_feature_names()
        }
        
        with open(f"{artifacts_dir}/metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2)
        
        logger.info(f"Model artifacts saved to {artifacts_dir}")
    
    def run_training_pipeline(self, model_names: Optional[list] = None) -> Dict[str, str]:
        """Run complete training pipeline"""
        logger.info("Starting training pipeline...")
        
        if model_names is None:
            model_names = list(self.model_configs.keys())
        
        # Load and validate data
        df = self.load_and_validate_data()
        
        # Prepare features
        X, y = self.prepare_features(df)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        run_ids = {}
        
        for model_name in model_names:
            logger.info(f"Training {model_name}...")
            
            # Optimize hyperparameters
            best_params = self.optimize_hyperparameters(X_train, y_train, model_name)
            
            # Train model
            model = self.train_model(X_train, y_train, model_name, best_params)
            
            # Evaluate model
            evaluation_results = self.evaluate_model(model, X_test, y_test)
            
            # Log to MLflow
            artifacts = {
                'classification_report': evaluation_results['classification_report'],
                'confusion_matrix': evaluation_results['confusion_matrix']
            }
            
            run_id = self.log_to_mlflow(
                model, best_params, evaluation_results, artifacts
            )
            
            # Save artifacts
            self.save_model_artifacts(
                model, self.data_preprocessor, self.feature_engineer, run_id
            )
            
            run_ids[model_name] = run_id
        
        logger.info("Training pipeline completed")
        return run_ids

def main():
    parser = argparse.ArgumentParser(description='Train fraud detection models')
    parser.add_argument('--config', type=str, default='config/training_config.json',
                       help='Path to training configuration file')
    parser.add_argument('--experiment-name', type=str, default='fraud-detection',
                       help='MLflow experiment name')
    parser.add_argument('--models', nargs='+', 
                       choices=['random_forest', 'gradient_boosting', 'logistic_regression'],
                       help='Models to train')
    
    args = parser.parse_args()
    
    # Load configuration
    if os.path.exists(args.config):
        with open(args.config, 'r') as f:
            config = json.load(f)
    else:
        config = {
            'data_path': 'data/processed/training_data.csv',
            'model_name': 'fraud-detection'
        }
    
    # Set MLflow experiment
    mlflow.set_experiment(args.experiment_name)
    
    # Initialize trainer
    trainer = ModelTrainer(config)
    
    # Run training
    run_ids = trainer.run_training_pipeline(args.models)
    
    # Print results
    print("Training completed successfully!")
    print("Run IDs:")
    for model_name, run_id in run_ids.items():
        print(f"  {model_name}: {run_id}")

if __name__ == "__main__":
    main()
