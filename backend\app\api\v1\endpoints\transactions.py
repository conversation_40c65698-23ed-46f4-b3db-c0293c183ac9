
"""
Transaction processing endpoints
"""

import time
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db_session
from app.core.exceptions import TransactionNotFoundException, ProcessingException
from app.core.logging import get_logger, log_transaction_event
from app.core.security import rate_limiter, RateLimitException
from app.schemas.transaction import (
    TransactionRequest,
    TransactionResponse,
    TransactionBatchRequest,
    TransactionBatchResponse,
    TransactionStats
)
from app.services.transaction_service import TransactionService
from app.services.fraud_detection_service import FraudDetectionService
from app.api.dependencies import get_current_user, require_permission

router = APIRouter()
logger = get_logger(__name__)


@router.post("/score", response_model=TransactionResponse)
async def score_transaction(
    request: TransactionRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(get_current_user)
):
    """
    Score a single transaction for fraud detection
    
    This endpoint processes a transaction in real-time and returns:
    - Fraud score (0.0 to 1.0)
    - Risk level (LOW, MEDIUM, HIGH)
    - Decision (ALLOW, REVIEW, BLOCK)
    - Processing time
    """
    start_time = time.time()
    
    # Rate limiting
    client_id = getattr(current_user, 'id', 'anonymous')
    if not await rate_limiter.is_allowed(f"transaction_score:{client_id}"):
        raise RateLimitException()
    
    try:
        # Initialize services
        transaction_service = TransactionService(db)
        fraud_service = FraudDetectionService()
        
        # Log transaction start
        log_transaction_event(
            logger,
            "Transaction scoring started",
            request.transaction_id or "auto-generated",
            user_id=getattr(current_user, 'id', None),
            amount=float(request.amount),
            type=request.type
        )
        
        # Process transaction
        result = await transaction_service.process_transaction(request)
        
        # Add background tasks for async processing
        background_tasks.add_task(
            fraud_service.send_alerts_if_needed,
            result
        )
        
        # Calculate processing time
        processing_time_ms = int((time.time() - start_time) * 1000)
        result.processing_time_ms = processing_time_ms
        
        # Log completion
        log_transaction_event(
            logger,
            "Transaction scoring completed",
            result.transaction_id,
            fraud_score=result.fraud_result.fraud_score if result.fraud_result else None,
            decision=result.fraud_result.decision if result.fraud_result else None,
            processing_time_ms=processing_time_ms
        )
        
        return result
        
    except Exception as e:
        logger.error(
            "Transaction scoring failed",
            transaction_id=request.transaction_id,
            error=str(e),
            processing_time_ms=int((time.time() - start_time) * 1000)
        )
        raise ProcessingException(f"Failed to process transaction: {str(e)}")


@router.post("/batch", response_model=TransactionBatchResponse)
async def score_transactions_batch(
    request: TransactionBatchRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(get_current_user)
):
    """
    Score multiple transactions in batch
    
    Processes up to 100 transactions at once for bulk fraud detection.
    Returns results for each transaction with individual scores and decisions.
    """
    start_time = time.time()
    
    # Validate batch size
    if len(request.transactions) > 100:
        raise HTTPException(
            status_code=400,
            detail="Batch size cannot exceed 100 transactions"
        )
    
    # Rate limiting for batch requests
    client_id = getattr(current_user, 'id', 'anonymous')
    if not await rate_limiter.is_allowed(f"transaction_batch:{client_id}", max_requests=10):
        raise RateLimitException()
    
    try:
        transaction_service = TransactionService(db)
        fraud_service = FraudDetectionService()
        
        logger.info(
            "Batch transaction scoring started",
            batch_size=len(request.transactions),
            user_id=getattr(current_user, 'id', None)
        )
        
        # Process batch
        results = await transaction_service.process_transaction_batch(request.transactions)
        
        # Add background tasks
        for result in results:
            background_tasks.add_task(
                fraud_service.send_alerts_if_needed,
                result
            )
        
        # Calculate processing time
        processing_time_ms = int((time.time() - start_time) * 1000)
        
        # Create batch response
        batch_response = TransactionBatchResponse(
            results=results,
            total_processed=len(results),
            processing_time_ms=processing_time_ms
        )
        
        logger.info(
            "Batch transaction scoring completed",
            batch_size=len(results),
            processing_time_ms=processing_time_ms
        )
        
        return batch_response
        
    except Exception as e:
        logger.error(
            "Batch transaction scoring failed",
            batch_size=len(request.transactions),
            error=str(e)
        )
        raise ProcessingException(f"Failed to process batch: {str(e)}")


@router.get("/{transaction_id}", response_model=TransactionResponse)
async def get_transaction(
    transaction_id: str,
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(get_current_user)
):
    """
    Get transaction details by ID
    
    Retrieves complete transaction information including fraud detection results.
    """
    try:
        transaction_service = TransactionService(db)
        result = await transaction_service.get_transaction(transaction_id)
        
        if not result:
            raise TransactionNotFoundException(transaction_id)
        
        return result
        
    except TransactionNotFoundException:
        raise
    except Exception as e:
        logger.error(
            "Failed to retrieve transaction",
            transaction_id=transaction_id,
            error=str(e)
        )
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve transaction"
        )


@router.get("/", response_model=List[TransactionResponse])
async def list_transactions(
    skip: int = Query(0, ge=0, description="Number of transactions to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of transactions to return"),
    status: Optional[str] = Query(None, description="Filter by transaction status"),
    fraud_only: bool = Query(False, description="Return only fraudulent transactions"),
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(get_current_user)
):
    """
    List transactions with optional filtering
    
    Returns paginated list of transactions with optional filters for status and fraud detection.
    """
    try:
        transaction_service = TransactionService(db)
        results = await transaction_service.list_transactions(
            skip=skip,
            limit=limit,
            status=status,
            fraud_only=fraud_only
        )
        
        return results
        
    except Exception as e:
        logger.error(
            "Failed to list transactions",
            error=str(e)
        )
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve transactions"
        )


@router.get("/stats/summary", response_model=TransactionStats)
async def get_transaction_stats(
    days: int = Query(7, ge=1, le=365, description="Number of days to include in stats"),
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(require_permission("read:analytics"))
):
    """
    Get transaction statistics
    
    Returns comprehensive statistics including fraud rates, processing times, and breakdowns by type.
    """
    try:
        transaction_service = TransactionService(db)
        stats = await transaction_service.get_transaction_stats(days=days)
        
        return stats
        
    except Exception as e:
        logger.error(
            "Failed to get transaction stats",
            error=str(e)
        )
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve transaction statistics"
        )

