import React, { useEffect, useState } from 'react';
import { useDashboardStore } from '@/store/dashboardStore';
import { useTransactionStore } from '@/store/transactionStore';
import { useAlertStore } from '@/store/alertStore';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import FraudTrendsChart from '@/components/dashboard/FraudTrendsChart';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { formatCurrency, formatNumber, formatPercentage } from '@/utils';

const Analytics: React.FC = () => {
  const [timeRange, setTimeRange] = useState('7d');
  const [selectedMetric, setSelectedMetric] = useState('fraud_rate');

  const {
    stats,
    transactionStats,
    fraudTrends,
    isLoading: dashboardLoading,
    fetchDashboardStats,
    fetchTransactionStats,
    fetchFraudTrends,
  } = useDashboardStore();

  const { transactions } = useTransactionStore();
  const { alerts } = useAlertStore();

  useEffect(() => {
    const loadAnalyticsData = async () => {
      await Promise.all([
        fetchDashboardStats(timeRange),
        fetchTransactionStats({ period: timeRange }),
        fetchFraudTrends({ period: timeRange }),
      ]);
    };

    loadAnalyticsData();
  }, [timeRange, fetchDashboardStats, fetchTransactionStats, fetchFraudTrends]);

  // Prepare chart data
  const transactionTypeData = transactions.reduce((acc, transaction) => {
    const type = transaction.type;
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const pieChartData = Object.entries(transactionTypeData).map(([type, count]) => ({
    name: type,
    value: count,
  }));

  const alertSeverityData = alerts.reduce((acc, alert) => {
    const severity = alert.severity;
    acc[severity] = (acc[severity] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const alertChartData = Object.entries(alertSeverityData).map(([severity, count]) => ({
    name: severity,
    value: count,
  }));

  const COLORS = ['#3b82f6', '#ef4444', '#f59e0b', '#10b981', '#8b5cf6'];

  if (dashboardLoading && !stats) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Analytics & Reporting
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Comprehensive fraud detection analytics and performance metrics
          </p>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4 space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="rounded-md border-gray-300 text-sm focus:border-primary-500 focus:ring-primary-500"
          >
            <option value="1d">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
          <Button
            onClick={() => {
              fetchDashboardStats(timeRange);
              fetchTransactionStats({ period: timeRange });
              fetchFraudTrends({ period: timeRange });
            }}
          >
            Refresh
          </Button>
        </div>
      </div>

      {/* Key metrics */}
      {stats && (
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <Card padding="sm">
            <div className="text-center">
              <dt className="text-sm font-medium text-gray-500">Detection Rate</dt>
              <dd className="mt-1 text-3xl font-semibold text-gray-900">
                {formatPercentage(stats.fraud_rate)}
              </dd>
            </div>
          </Card>
          <Card padding="sm">
            <div className="text-center">
              <dt className="text-sm font-medium text-gray-500">Amount Protected</dt>
              <dd className="mt-1 text-3xl font-semibold text-green-600">
                {formatCurrency(stats.blocked_amount)}
              </dd>
            </div>
          </Card>
          <Card padding="sm">
            <div className="text-center">
              <dt className="text-sm font-medium text-gray-500">Avg Response Time</dt>
              <dd className="mt-1 text-3xl font-semibold text-blue-600">
                {stats.processing_time_avg.toFixed(0)}ms
              </dd>
            </div>
          </Card>
          <Card padding="sm">
            <div className="text-center">
              <dt className="text-sm font-medium text-gray-500">Active Alerts</dt>
              <dd className="mt-1 text-3xl font-semibold text-red-600">
                {formatNumber(stats.alerts_count)}
              </dd>
            </div>
          </Card>
        </div>
      )}

      {/* Charts grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Fraud trends */}
        <div className="lg:col-span-2">
          <FraudTrendsChart />
        </div>

        {/* Transaction types distribution */}
        <Card>
          <Card.Header>
            <h3 className="text-lg font-medium text-gray-900">Transaction Types</h3>
            <p className="text-sm text-gray-500">Distribution by transaction type</p>
          </Card.Header>
          <Card.Body>
            {pieChartData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={pieChartData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {pieChartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="text-center py-8 text-gray-500">No data available</div>
            )}
          </Card.Body>
        </Card>

        {/* Alert severity distribution */}
        <Card>
          <Card.Header>
            <h3 className="text-lg font-medium text-gray-900">Alert Severity</h3>
            <p className="text-sm text-gray-500">Distribution by severity level</p>
          </Card.Header>
          <Card.Body>
            {alertChartData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={alertChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="value" fill="#ef4444" />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="text-center py-8 text-gray-500">No data available</div>
            )}
          </Card.Body>
        </Card>

        {/* Performance metrics */}
        <Card>
          <Card.Header>
            <h3 className="text-lg font-medium text-gray-900">Performance Metrics</h3>
            <p className="text-sm text-gray-500">System performance indicators</p>
          </Card.Header>
          <Card.Body>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-500">Accuracy Rate</span>
                <span className="text-sm font-semibold text-gray-900">98.5%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-green-600 h-2 rounded-full" style={{ width: '98.5%' }}></div>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-500">False Positive Rate</span>
                <span className="text-sm font-semibold text-gray-900">1.2%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '1.2%' }}></div>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-500">System Uptime</span>
                <span className="text-sm font-semibold text-gray-900">99.9%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-blue-600 h-2 rounded-full" style={{ width: '99.9%' }}></div>
              </div>
            </div>
          </Card.Body>
        </Card>

        {/* Top risk factors */}
        <Card>
          <Card.Header>
            <h3 className="text-lg font-medium text-gray-900">Top Risk Factors</h3>
            <p className="text-sm text-gray-500">Most common fraud indicators</p>
          </Card.Header>
          <Card.Body>
            <div className="space-y-3">
              {[
                { factor: 'High transaction amount', percentage: 45 },
                { factor: 'Unusual time pattern', percentage: 32 },
                { factor: 'New account activity', percentage: 28 },
                { factor: 'Geographic anomaly', percentage: 22 },
                { factor: 'Velocity check failure', percentage: 18 },
              ].map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">{item.factor}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-red-500 h-2 rounded-full"
                        style={{ width: `${item.percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium text-gray-900 w-8">
                      {item.percentage}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </Card.Body>
        </Card>
      </div>

      {/* Export options */}
      <Card>
        <Card.Header>
          <h3 className="text-lg font-medium text-gray-900">Export & Reports</h3>
          <p className="text-sm text-gray-500">Download analytics data and generate reports</p>
        </Card.Header>
        <Card.Body>
          <div className="flex flex-wrap gap-3">
            <Button variant="outline">Export CSV</Button>
            <Button variant="outline">Export PDF</Button>
            <Button variant="outline">Generate Report</Button>
            <Button variant="outline">Schedule Report</Button>
          </div>
        </Card.Body>
      </Card>
    </div>
  );
};

export default Analytics;
