"""
Configuration settings for ML service
"""

import os
from typing import Optional, List
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """Application settings"""
    
    # Application
    VERSION: str = "1.0.0"
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    DEBUG: bool = Field(default=True, env="DEBUG")
    
    # Model Configuration
    MODEL_PATH: str = Field(default="/models/fraud_detection_model.pkl", env="MODEL_PATH")
    MODEL_VERSION: str = Field(default="1.0.0", env="MODEL_VERSION")
    MODEL_TYPE: str = Field(default="ensemble", env="MODEL_TYPE")  # ensemble, neural, anomaly
    
    # Feature Store
    FEATURE_STORE_URL: str = Field(default="http://feature-store:8002", env="FEATURE_STORE_URL")
    FEATURE_CACHE_TTL: int = Field(default=300, env="FEATURE_CACHE_TTL")  # 5 minutes
    
    # Redis Configuration
    REDIS_HOST: str = Field(default="localhost", env="REDIS_HOST")
    REDIS_PORT: int = Field(default=6379, env="REDIS_PORT")
    REDIS_DB: int = Field(default=0, env="REDIS_DB")
    REDIS_PASSWORD: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    
    # MLflow Configuration
    MLFLOW_TRACKING_URI: str = Field(default="http://localhost:5000", env="MLFLOW_TRACKING_URI")
    MLFLOW_EXPERIMENT_NAME: str = Field(default="fraud_detection", env="MLFLOW_EXPERIMENT_NAME")
    
    # Model Training
    TRAINING_DATA_PATH: str = Field(default="/data/fraud_detection_samples.csv", env="TRAINING_DATA_PATH")
    MODEL_RETRAIN_INTERVAL: int = Field(default=24, env="MODEL_RETRAIN_INTERVAL")  # hours
    
    # Performance Thresholds
    FRAUD_THRESHOLD: float = Field(default=0.5, env="FRAUD_THRESHOLD")
    HIGH_RISK_THRESHOLD: float = Field(default=0.8, env="HIGH_RISK_THRESHOLD")
    PREDICTION_TIMEOUT: float = Field(default=1.0, env="PREDICTION_TIMEOUT")  # seconds
    
    # Ensemble Configuration
    ENSEMBLE_MODELS: List[str] = Field(
        default=["random_forest", "xgboost", "lightgbm", "neural_network"],
        env="ENSEMBLE_MODELS"
    )
    ENSEMBLE_WEIGHTS: Optional[List[float]] = Field(default=None, env="ENSEMBLE_WEIGHTS")
    
    # Neural Network Configuration
    NN_HIDDEN_LAYERS: List[int] = Field(default=[128, 64, 32], env="NN_HIDDEN_LAYERS")
    NN_DROPOUT_RATE: float = Field(default=0.3, env="NN_DROPOUT_RATE")
    NN_LEARNING_RATE: float = Field(default=0.001, env="NN_LEARNING_RATE")
    NN_BATCH_SIZE: int = Field(default=256, env="NN_BATCH_SIZE")
    NN_EPOCHS: int = Field(default=100, env="NN_EPOCHS")
    
    # Anomaly Detection Configuration
    ANOMALY_CONTAMINATION: float = Field(default=0.1, env="ANOMALY_CONTAMINATION")
    ANOMALY_N_ESTIMATORS: int = Field(default=100, env="ANOMALY_N_ESTIMATORS")
    
    # Data Processing
    FEATURE_SCALING: str = Field(default="standard", env="FEATURE_SCALING")  # standard, minmax, robust
    HANDLE_MISSING: str = Field(default="median", env="HANDLE_MISSING")  # median, mean, mode, drop
    
    # Monitoring
    ENABLE_DRIFT_DETECTION: bool = Field(default=True, env="ENABLE_DRIFT_DETECTION")
    DRIFT_THRESHOLD: float = Field(default=0.1, env="DRIFT_THRESHOLD")
    
    # Security
    API_KEY: Optional[str] = Field(default=None, env="API_KEY")
    ENABLE_AUTH: bool = Field(default=False, env="ENABLE_AUTH")
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()
