// API Response Types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Authentication Types
export interface User {
  id: string;
  email: string;
  full_name: string;
  role: UserRole;
  permissions: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
  last_login?: string;
}

export type UserRole = 'admin' | 'analyst' | 'operator' | 'user';

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  full_name: string;
  role?: UserRole;
}

// Transaction Types
export interface Transaction {
  id: string;
  transaction_id: string;
  step: number;
  type: TransactionType;
  amount: number;
  name_orig: string;
  oldbalance_orig: number;
  newbalance_orig: number;
  name_dest: string;
  oldbalance_dest: number;
  newbalance_dest: number;
  is_fraud: boolean;
  is_flagged_fraud: boolean;
  created_at: string;
  updated_at: string;
  fraud_result?: FraudResult;
}

export type TransactionType = 'PAYMENT' | 'TRANSFER' | 'CASH_OUT' | 'DEBIT' | 'CASH_IN';

export interface TransactionRequest {
  transaction_id?: string;
  step: number;
  type: TransactionType;
  amount: number;
  name_orig: string;
  oldbalance_orig: number;
  newbalance_orig: number;
  name_dest: string;
  oldbalance_dest: number;
  newbalance_dest: number;
}

export interface FraudResult {
  fraud_score: number;
  is_fraudulent: boolean;
  decision: FraudDecision;
  reason_codes: string[];
  confidence?: number;
  model_version?: string;
  processing_time_ms: number;
  risk_level: RiskLevel;
}

export type FraudDecision = 'ALLOW' | 'REVIEW' | 'BLOCK';
export type RiskLevel = 'LOW' | 'MEDIUM' | 'HIGH';

// Alert Types
export interface Alert {
  id: string;
  transaction_id: string;
  alert_type: AlertType;
  severity: AlertSeverity;
  title: string;
  description: string;
  status: AlertStatus;
  created_at: string;
  updated_at: string;
  resolved_at?: string;
  resolved_by?: string;
  resolution_notes?: string;
  metadata?: Record<string, any>;
}

export type AlertType = 'FRAUD_DETECTED' | 'HIGH_RISK_TRANSACTION' | 'SUSPICIOUS_PATTERN' | 'SYSTEM_ANOMALY';
export type AlertSeverity = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
export type AlertStatus = 'OPEN' | 'IN_PROGRESS' | 'RESOLVED' | 'FALSE_POSITIVE';

// Analytics Types
export interface DashboardStats {
  total_transactions: number;
  fraud_transactions: number;
  fraud_rate: number;
  total_amount: number;
  fraud_amount: number;
  blocked_amount: number;
  alerts_count: number;
  processing_time_avg: number;
}

export interface TransactionStats {
  period: string;
  total_count: number;
  fraud_count: number;
  total_amount: number;
  fraud_amount: number;
  fraud_rate: number;
}

export interface ChartDataPoint {
  timestamp: string;
  value: number;
  label?: string;
}

// WebSocket Types
export interface WebSocketMessage {
  type: 'transaction' | 'alert' | 'stats' | 'system';
  data: any;
  timestamp: string;
}

export interface RealTimeTransaction {
  transaction: Transaction;
  fraud_result: FraudResult;
}

export interface RealTimeAlert {
  alert: Alert;
  transaction?: Transaction;
}

// UI State Types
export interface LoadingState {
  isLoading: boolean;
  error?: string;
}

export interface TableColumn<T = any> {
  key: keyof T;
  title: string;
  sortable?: boolean;
  render?: (value: any, record: T) => React.ReactNode;
  width?: string;
}

export interface FilterOption {
  label: string;
  value: string;
}

export interface DateRange {
  start: Date;
  end: Date;
}

// Form Types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea' | 'date';
  required?: boolean;
  placeholder?: string;
  options?: FilterOption[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: RegExp;
    message?: string;
  };
}

// Notification Types
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}
