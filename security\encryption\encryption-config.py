"""
Data Encryption Configuration and Utilities
Provides encryption at rest and in transit for sensitive data
"""

import os
import base64
import hashlib
from typing import Optional, Dict, Any, Union
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.backends import default_backend
import boto3
from azure.keyvault.secrets import SecretClient
from azure.identity import DefaultAzureCredential
from google.cloud import kms
import logging

logger = logging.getLogger(__name__)

class EncryptionManager:
    """Centralized encryption management for the fraud detection system"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.backend = default_backend()
        self._initialize_encryption()
    
    def _initialize_encryption(self):
        """Initialize encryption based on configuration"""
        encryption_type = self.config.get('encryption_type', 'local')
        
        if encryption_type == 'aws_kms':
            self._init_aws_kms()
        elif encryption_type == 'azure_keyvault':
            self._init_azure_keyvault()
        elif encryption_type == 'gcp_kms':
            self._init_gcp_kms()
        else:
            self._init_local_encryption()
    
    def _init_aws_kms(self):
        """Initialize AWS KMS encryption"""
        self.kms_client = boto3.client(
            'kms',
            region_name=self.config.get('aws_region', 'us-east-1')
        )
        self.kms_key_id = self.config['aws_kms_key_id']
        logger.info("Initialized AWS KMS encryption")
    
    def _init_azure_keyvault(self):
        """Initialize Azure Key Vault encryption"""
        credential = DefaultAzureCredential()
        vault_url = self.config['azure_keyvault_url']
        self.keyvault_client = SecretClient(vault_url=vault_url, credential=credential)
        logger.info("Initialized Azure Key Vault encryption")
    
    def _init_gcp_kms(self):
        """Initialize Google Cloud KMS encryption"""
        self.gcp_kms_client = kms.KeyManagementServiceClient()
        self.gcp_key_name = self.config['gcp_kms_key_name']
        logger.info("Initialized Google Cloud KMS encryption")
    
    def _init_local_encryption(self):
        """Initialize local encryption with Fernet"""
        key = self.config.get('encryption_key')
        if not key:
            key = Fernet.generate_key()
            logger.warning("Generated new encryption key. Store securely!")
        
        if isinstance(key, str):
            key = key.encode()
        
        self.fernet = Fernet(key)
        logger.info("Initialized local Fernet encryption")

class FieldLevelEncryption:
    """Field-level encryption for sensitive PII data"""
    
    def __init__(self, encryption_manager: EncryptionManager):
        self.encryption_manager = encryption_manager
        
        # Define which fields require encryption
        self.encrypted_fields = {
            'users': ['email', 'phone', 'ssn', 'address'],
            'transactions': ['account_number', 'routing_number', 'card_number'],
            'alerts': ['customer_data', 'sensitive_metadata']
        }
    
    def encrypt_field(self, table: str, field: str, value: str) -> str:
        """Encrypt a specific field value"""
        if not self._should_encrypt(table, field):
            return value
        
        try:
            if hasattr(self.encryption_manager, 'fernet'):
                encrypted = self.encryption_manager.fernet.encrypt(value.encode())
                return base64.b64encode(encrypted).decode()
            else:
                # Use cloud KMS
                return self._encrypt_with_kms(value)
        except Exception as e:
            logger.error(f"Failed to encrypt field {table}.{field}: {e}")
            raise
    
    def decrypt_field(self, table: str, field: str, encrypted_value: str) -> str:
        """Decrypt a specific field value"""
        if not self._should_encrypt(table, field):
            return encrypted_value
        
        try:
            if hasattr(self.encryption_manager, 'fernet'):
                encrypted_bytes = base64.b64decode(encrypted_value.encode())
                decrypted = self.encryption_manager.fernet.decrypt(encrypted_bytes)
                return decrypted.decode()
            else:
                # Use cloud KMS
                return self._decrypt_with_kms(encrypted_value)
        except Exception as e:
            logger.error(f"Failed to decrypt field {table}.{field}: {e}")
            raise
    
    def _should_encrypt(self, table: str, field: str) -> bool:
        """Check if a field should be encrypted"""
        return field in self.encrypted_fields.get(table, [])
    
    def _encrypt_with_kms(self, value: str) -> str:
        """Encrypt using cloud KMS"""
        # Implementation depends on the specific KMS provider
        if hasattr(self.encryption_manager, 'kms_client'):
            # AWS KMS
            response = self.encryption_manager.kms_client.encrypt(
                KeyId=self.encryption_manager.kms_key_id,
                Plaintext=value.encode()
            )
            return base64.b64encode(response['CiphertextBlob']).decode()
        
        # Add other KMS providers as needed
        raise NotImplementedError("KMS encryption not implemented for this provider")
    
    def _decrypt_with_kms(self, encrypted_value: str) -> str:
        """Decrypt using cloud KMS"""
        if hasattr(self.encryption_manager, 'kms_client'):
            # AWS KMS
            ciphertext_blob = base64.b64decode(encrypted_value.encode())
            response = self.encryption_manager.kms_client.decrypt(
                CiphertextBlob=ciphertext_blob
            )
            return response['Plaintext'].decode()
        
        raise NotImplementedError("KMS decryption not implemented for this provider")

class DatabaseEncryption:
    """Database-level encryption utilities"""
    
    @staticmethod
    def generate_database_key() -> bytes:
        """Generate a strong database encryption key"""
        return os.urandom(32)  # 256-bit key
    
    @staticmethod
    def derive_key_from_password(password: str, salt: bytes) -> bytes:
        """Derive encryption key from password using PBKDF2"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
            backend=default_backend()
        )
        return kdf.derive(password.encode())
    
    @staticmethod
    def encrypt_database_backup(data: bytes, key: bytes) -> bytes:
        """Encrypt database backup data"""
        # Generate random IV
        iv = os.urandom(16)
        
        # Create cipher
        cipher = Cipher(
            algorithms.AES(key),
            modes.CBC(iv),
            backend=default_backend()
        )
        
        # Encrypt data
        encryptor = cipher.encryptor()
        
        # Pad data to block size
        block_size = 16
        padding_length = block_size - (len(data) % block_size)
        padded_data = data + bytes([padding_length] * padding_length)
        
        encrypted_data = encryptor.update(padded_data) + encryptor.finalize()
        
        # Return IV + encrypted data
        return iv + encrypted_data

class TokenEncryption:
    """JWT and API token encryption utilities"""
    
    def __init__(self, private_key_path: str, public_key_path: str):
        self.private_key = self._load_private_key(private_key_path)
        self.public_key = self._load_public_key(public_key_path)
    
    def _load_private_key(self, path: str):
        """Load RSA private key"""
        with open(path, 'rb') as key_file:
            return serialization.load_pem_private_key(
                key_file.read(),
                password=None,
                backend=default_backend()
            )
    
    def _load_public_key(self, path: str):
        """Load RSA public key"""
        with open(path, 'rb') as key_file:
            return serialization.load_pem_public_key(
                key_file.read(),
                backend=default_backend()
            )
    
    def encrypt_token_payload(self, payload: str) -> str:
        """Encrypt token payload with RSA"""
        encrypted = self.public_key.encrypt(
            payload.encode(),
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
        return base64.b64encode(encrypted).decode()
    
    def decrypt_token_payload(self, encrypted_payload: str) -> str:
        """Decrypt token payload with RSA"""
        encrypted_bytes = base64.b64decode(encrypted_payload.encode())
        decrypted = self.private_key.decrypt(
            encrypted_bytes,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
        return decrypted.decode()

class HashingUtilities:
    """Secure hashing utilities for passwords and sensitive data"""
    
    @staticmethod
    def hash_password(password: str, salt: Optional[bytes] = None) -> tuple[str, str]:
        """Hash password with salt using PBKDF2"""
        if salt is None:
            salt = os.urandom(32)
        
        pwdhash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt,
            100000  # 100,000 iterations
        )
        
        return (
            base64.b64encode(pwdhash).decode('ascii'),
            base64.b64encode(salt).decode('ascii')
        )
    
    @staticmethod
    def verify_password(password: str, stored_hash: str, stored_salt: str) -> bool:
        """Verify password against stored hash"""
        salt = base64.b64decode(stored_salt.encode('ascii'))
        pwdhash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt,
            100000
        )
        
        stored_pwdhash = base64.b64decode(stored_hash.encode('ascii'))
        return pwdhash == stored_pwdhash
    
    @staticmethod
    def hash_sensitive_data(data: str) -> str:
        """Hash sensitive data for indexing/searching"""
        return hashlib.sha256(data.encode()).hexdigest()

# Configuration factory
def create_encryption_manager(environment: str = 'development') -> EncryptionManager:
    """Create encryption manager based on environment"""
    
    if environment == 'production':
        config = {
            'encryption_type': os.getenv('ENCRYPTION_TYPE', 'aws_kms'),
            'aws_kms_key_id': os.getenv('AWS_KMS_KEY_ID'),
            'aws_region': os.getenv('AWS_REGION', 'us-east-1'),
            'azure_keyvault_url': os.getenv('AZURE_KEYVAULT_URL'),
            'gcp_kms_key_name': os.getenv('GCP_KMS_KEY_NAME'),
        }
    else:
        config = {
            'encryption_type': 'local',
            'encryption_key': os.getenv('ENCRYPTION_KEY', Fernet.generate_key())
        }
    
    return EncryptionManager(config)
