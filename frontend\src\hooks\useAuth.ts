import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '@/store/authStore';
import { hasPermission } from '@/utils';

export function useAuth() {
  const {
    user,
    isAuthenticated,
    isLoading,
    error,
    login,
    register,
    logout,
    getCurrentUser,
    clearError,
  } = useAuthStore();

  return {
    user,
    isAuthenticated,
    isLoading,
    error,
    login,
    register,
    logout,
    getCurrentUser,
    clearError,
  };
}

export function useRequireAuth() {
  const { isAuthenticated, isLoading } = useAuthStore();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate('/login', { 
        state: { from: location.pathname },
        replace: true 
      });
    }
  }, [isAuthenticated, isLoading, navigate, location]);

  return { isAuthenticated, isLoading };
}

export function usePermissions() {
  const { user } = useAuthStore();

  const checkPermission = (permission: string): boolean => {
    if (!user) return false;
    return hasPermission(user.permissions, permission);
  };

  const checkRole = (role: string): boolean => {
    if (!user) return false;
    return user.role === role;
  };

  const checkAnyRole = (roles: string[]): boolean => {
    if (!user) return false;
    return roles.includes(user.role);
  };

  return {
    checkPermission,
    checkRole,
    checkAnyRole,
    permissions: user?.permissions || [],
    role: user?.role,
  };
}
