#!/usr/bin/env python3
"""
Model Validation Pipeline
Comprehensive model validation including performance, fairness, and robustness checks
"""

import os
import sys
import json
import logging
import argparse
from typing import Dict, Any, List, Tuple
import pandas as pd
import numpy as np
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
from sklearn.model_selection import cross_val_score
import mlflow
from mlflow.tracking import MlflowClient
import joblib
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ml_service.src.model_evaluation import ModelEvaluator
from ml_service.src.data_drift_detector import DataDriftDetector

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelValidator:
    """Comprehensive model validation framework"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.mlflow_client = MlflowClient()
        self.model_evaluator = ModelEvaluator()
        self.drift_detector = DataDriftDetector()
        
        # Validation thresholds
        self.thresholds = {
            'min_roc_auc': 0.85,
            'min_precision': 0.80,
            'min_recall': 0.75,
            'max_false_positive_rate': 0.05,
            'min_f1_score': 0.77,
            'max_prediction_drift': 0.1,
            'max_feature_drift': 0.15
        }
        
        # Update thresholds from config
        self.thresholds.update(config.get('validation_thresholds', {}))
    
    def load_model_and_data(self, run_id: str) -> Tuple[Any, pd.DataFrame, pd.Series]:
        """Load model and validation data"""
        logger.info(f"Loading model from run {run_id}...")
        
        # Load model from MLflow
        model_uri = f"runs:/{run_id}/model"
        model = mlflow.sklearn.load_model(model_uri)
        
        # Load validation data
        validation_data_path = self.config.get('validation_data_path', 'data/processed/validation_data.csv')
        
        if not os.path.exists(validation_data_path):
            raise FileNotFoundError(f"Validation data not found at {validation_data_path}")
        
        df = pd.read_csv(validation_data_path)
        
        # Separate features and target
        target_column = 'isFraud'
        X = df.drop(columns=[target_column])
        y = df[target_column]
        
        logger.info(f"Loaded model and {len(df)} validation samples")
        return model, X, y
    
    def validate_performance_metrics(self, model: Any, X: pd.DataFrame, y: pd.Series) -> Dict[str, Any]:
        """Validate model performance metrics"""
        logger.info("Validating performance metrics...")
        
        # Predictions
        y_pred = model.predict(X)
        y_pred_proba = model.predict_proba(X)[:, 1]
        
        # Calculate metrics
        metrics = self.model_evaluator.calculate_metrics(y, y_pred, y_pred_proba)
        
        # Validation results
        validation_results = {
            'metrics': metrics,
            'passed': True,
            'failed_checks': []
        }
        
        # Check thresholds
        checks = [
            ('roc_auc', 'min_roc_auc', '>='),
            ('precision', 'min_precision', '>='),
            ('recall', 'min_recall', '>='),
            ('f1_score', 'min_f1_score', '>='),
            ('false_positive_rate', 'max_false_positive_rate', '<=')
        ]
        
        for metric_name, threshold_name, operator in checks:
            metric_value = metrics.get(metric_name)
            threshold_value = self.thresholds.get(threshold_name)
            
            if metric_value is not None and threshold_value is not None:
                if operator == '>=' and metric_value < threshold_value:
                    validation_results['passed'] = False
                    validation_results['failed_checks'].append({
                        'check': f"{metric_name} >= {threshold_value}",
                        'actual': metric_value,
                        'expected': threshold_value
                    })
                elif operator == '<=' and metric_value > threshold_value:
                    validation_results['passed'] = False
                    validation_results['failed_checks'].append({
                        'check': f"{metric_name} <= {threshold_value}",
                        'actual': metric_value,
                        'expected': threshold_value
                    })
        
        logger.info(f"Performance validation: {'PASSED' if validation_results['passed'] else 'FAILED'}")
        return validation_results
    
    def validate_cross_validation_stability(self, model: Any, X: pd.DataFrame, y: pd.Series) -> Dict[str, Any]:
        """Validate model stability using cross-validation"""
        logger.info("Validating cross-validation stability...")
        
        # Perform cross-validation
        cv_scores = cross_val_score(model, X, y, cv=5, scoring='roc_auc')
        
        # Calculate statistics
        mean_score = cv_scores.mean()
        std_score = cv_scores.std()
        coefficient_of_variation = std_score / mean_score if mean_score > 0 else float('inf')
        
        # Stability check (CV should be < 0.05 for stable model)
        is_stable = coefficient_of_variation < 0.05
        
        validation_results = {
            'cv_scores': cv_scores.tolist(),
            'mean_score': mean_score,
            'std_score': std_score,
            'coefficient_of_variation': coefficient_of_variation,
            'is_stable': is_stable,
            'passed': is_stable
        }
        
        logger.info(f"CV stability validation: {'PASSED' if is_stable else 'FAILED'}")
        return validation_results
    
    def validate_fairness(self, model: Any, X: pd.DataFrame, y: pd.Series) -> Dict[str, Any]:
        """Validate model fairness across different groups"""
        logger.info("Validating model fairness...")
        
        # Get predictions
        y_pred = model.predict(X)
        y_pred_proba = model.predict_proba(X)[:, 1]
        
        fairness_results = {
            'passed': True,
            'group_metrics': {},
            'fairness_violations': []
        }
        
        # Check fairness across transaction types (if available)
        if 'type' in X.columns:
            transaction_types = X['type'].unique()
            
            for tx_type in transaction_types:
                mask = X['type'] == tx_type
                if mask.sum() > 50:  # Minimum sample size
                    group_y = y[mask]
                    group_pred = y_pred[mask]
                    group_pred_proba = y_pred_proba[mask]
                    
                    # Calculate metrics for this group
                    group_metrics = self.model_evaluator.calculate_metrics(
                        group_y, group_pred, group_pred_proba
                    )
                    
                    fairness_results['group_metrics'][tx_type] = group_metrics
            
            # Check for significant differences in performance
            if len(fairness_results['group_metrics']) > 1:
                roc_aucs = [metrics['roc_auc'] for metrics in fairness_results['group_metrics'].values()]
                max_diff = max(roc_aucs) - min(roc_aucs)
                
                if max_diff > 0.1:  # 10% difference threshold
                    fairness_results['passed'] = False
                    fairness_results['fairness_violations'].append({
                        'type': 'performance_disparity',
                        'max_roc_auc_difference': max_diff,
                        'threshold': 0.1
                    })
        
        logger.info(f"Fairness validation: {'PASSED' if fairness_results['passed'] else 'FAILED'}")
        return fairness_results
    
    def validate_data_drift(self, X: pd.DataFrame) -> Dict[str, Any]:
        """Validate for data drift compared to training data"""
        logger.info("Validating data drift...")
        
        # Load reference (training) data
        training_data_path = self.config.get('training_data_path', 'data/processed/training_data.csv')
        
        if not os.path.exists(training_data_path):
            logger.warning("Training data not found, skipping drift validation")
            return {'passed': True, 'message': 'Training data not available for drift detection'}
        
        training_df = pd.read_csv(training_data_path)
        X_train = training_df.drop(columns=['isFraud'])
        
        # Detect drift
        drift_results = self.drift_detector.detect_drift(X_train, X)
        
        # Check drift thresholds
        feature_drift_violations = []
        for feature, drift_score in drift_results.get('feature_drift', {}).items():
            if drift_score > self.thresholds['max_feature_drift']:
                feature_drift_violations.append({
                    'feature': feature,
                    'drift_score': drift_score,
                    'threshold': self.thresholds['max_feature_drift']
                })
        
        validation_results = {
            'drift_results': drift_results,
            'feature_drift_violations': feature_drift_violations,
            'passed': len(feature_drift_violations) == 0
        }
        
        logger.info(f"Data drift validation: {'PASSED' if validation_results['passed'] else 'FAILED'}")
        return validation_results
    
    def validate_robustness(self, model: Any, X: pd.DataFrame, y: pd.Series) -> Dict[str, Any]:
        """Validate model robustness to input perturbations"""
        logger.info("Validating model robustness...")
        
        # Original predictions
        original_pred_proba = model.predict_proba(X)[:, 1]
        
        robustness_results = {
            'passed': True,
            'perturbation_tests': []
        }
        
        # Test with small random noise
        for noise_level in [0.01, 0.05, 0.1]:
            X_noisy = X.copy()
            
            # Add noise to numerical columns
            numerical_cols = X.select_dtypes(include=[np.number]).columns
            for col in numerical_cols:
                noise = np.random.normal(0, noise_level * X[col].std(), len(X))
                X_noisy[col] = X[col] + noise
            
            # Get predictions with noise
            noisy_pred_proba = model.predict_proba(X_noisy)[:, 1]
            
            # Calculate prediction stability
            prediction_diff = np.abs(original_pred_proba - noisy_pred_proba)
            mean_diff = prediction_diff.mean()
            max_diff = prediction_diff.max()
            
            test_result = {
                'noise_level': noise_level,
                'mean_prediction_diff': mean_diff,
                'max_prediction_diff': max_diff,
                'passed': mean_diff < 0.1  # 10% threshold
            }
            
            robustness_results['perturbation_tests'].append(test_result)
            
            if not test_result['passed']:
                robustness_results['passed'] = False
        
        logger.info(f"Robustness validation: {'PASSED' if robustness_results['passed'] else 'FAILED'}")
        return robustness_results
    
    def validate_business_rules(self, model: Any, X: pd.DataFrame, y: pd.Series) -> Dict[str, Any]:
        """Validate business rule compliance"""
        logger.info("Validating business rules...")
        
        y_pred_proba = model.predict_proba(X)[:, 1]
        
        business_rules_results = {
            'passed': True,
            'rule_violations': []
        }
        
        # Rule 1: High amount transactions should have higher fraud probability
        if 'amount' in X.columns:
            high_amount_mask = X['amount'] > X['amount'].quantile(0.95)
            low_amount_mask = X['amount'] < X['amount'].quantile(0.05)
            
            if high_amount_mask.sum() > 0 and low_amount_mask.sum() > 0:
                high_amount_fraud_rate = y_pred_proba[high_amount_mask].mean()
                low_amount_fraud_rate = y_pred_proba[low_amount_mask].mean()
                
                if high_amount_fraud_rate <= low_amount_fraud_rate:
                    business_rules_results['passed'] = False
                    business_rules_results['rule_violations'].append({
                        'rule': 'High amount transactions should have higher fraud probability',
                        'high_amount_rate': high_amount_fraud_rate,
                        'low_amount_rate': low_amount_fraud_rate
                    })
        
        # Rule 2: Model should not predict fraud for very small amounts (< $1)
        if 'amount' in X.columns:
            very_small_mask = X['amount'] < 1.0
            if very_small_mask.sum() > 0:
                small_amount_fraud_rate = y_pred_proba[very_small_mask].mean()
                
                if small_amount_fraud_rate > 0.1:  # 10% threshold
                    business_rules_results['passed'] = False
                    business_rules_results['rule_violations'].append({
                        'rule': 'Very small transactions should rarely be flagged as fraud',
                        'small_amount_fraud_rate': small_amount_fraud_rate,
                        'threshold': 0.1
                    })
        
        logger.info(f"Business rules validation: {'PASSED' if business_rules_results['passed'] else 'FAILED'}")
        return business_rules_results
    
    def generate_validation_report(self, run_id: str, validation_results: Dict[str, Any]) -> str:
        """Generate comprehensive validation report"""
        logger.info("Generating validation report...")
        
        # Overall validation status
        overall_passed = all(result.get('passed', True) for result in validation_results.values())
        
        report = {
            'run_id': run_id,
            'validation_timestamp': pd.Timestamp.now().isoformat(),
            'overall_status': 'PASSED' if overall_passed else 'FAILED',
            'validation_results': validation_results,
            'thresholds_used': self.thresholds
        }
        
        # Save report
        report_path = f"validation_reports/validation_report_{run_id}.json"
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"Validation report saved to {report_path}")
        return report_path
    
    def run_validation_pipeline(self, run_id: str) -> Dict[str, Any]:
        """Run complete validation pipeline"""
        logger.info(f"Starting validation pipeline for run {run_id}...")
        
        # Load model and data
        model, X, y = self.load_model_and_data(run_id)
        
        # Run all validations
        validation_results = {}
        
        try:
            validation_results['performance'] = self.validate_performance_metrics(model, X, y)
        except Exception as e:
            logger.error(f"Performance validation failed: {e}")
            validation_results['performance'] = {'passed': False, 'error': str(e)}
        
        try:
            validation_results['stability'] = self.validate_cross_validation_stability(model, X, y)
        except Exception as e:
            logger.error(f"Stability validation failed: {e}")
            validation_results['stability'] = {'passed': False, 'error': str(e)}
        
        try:
            validation_results['fairness'] = self.validate_fairness(model, X, y)
        except Exception as e:
            logger.error(f"Fairness validation failed: {e}")
            validation_results['fairness'] = {'passed': False, 'error': str(e)}
        
        try:
            validation_results['data_drift'] = self.validate_data_drift(X)
        except Exception as e:
            logger.error(f"Data drift validation failed: {e}")
            validation_results['data_drift'] = {'passed': False, 'error': str(e)}
        
        try:
            validation_results['robustness'] = self.validate_robustness(model, X, y)
        except Exception as e:
            logger.error(f"Robustness validation failed: {e}")
            validation_results['robustness'] = {'passed': False, 'error': str(e)}
        
        try:
            validation_results['business_rules'] = self.validate_business_rules(model, X, y)
        except Exception as e:
            logger.error(f"Business rules validation failed: {e}")
            validation_results['business_rules'] = {'passed': False, 'error': str(e)}
        
        # Generate report
        report_path = self.generate_validation_report(run_id, validation_results)
        
        # Log validation results to MLflow
        with mlflow.start_run(run_id=run_id):
            for validation_type, results in validation_results.items():
                mlflow.log_metric(f"validation_{validation_type}_passed", 1 if results.get('passed') else 0)
            
            mlflow.log_artifact(report_path)
        
        overall_passed = all(result.get('passed', True) for result in validation_results.values())
        logger.info(f"Validation pipeline completed: {'PASSED' if overall_passed else 'FAILED'}")
        
        return validation_results

def main():
    parser = argparse.ArgumentParser(description='Validate fraud detection model')
    parser.add_argument('--run-id', type=str, required=True,
                       help='MLflow run ID to validate')
    parser.add_argument('--config', type=str, default='config/validation_config.json',
                       help='Path to validation configuration file')
    
    args = parser.parse_args()
    
    # Load configuration
    if os.path.exists(args.config):
        with open(args.config, 'r') as f:
            config = json.load(f)
    else:
        config = {
            'validation_data_path': 'data/processed/validation_data.csv',
            'training_data_path': 'data/processed/training_data.csv'
        }
    
    # Initialize validator
    validator = ModelValidator(config)
    
    # Run validation
    results = validator.run_validation_pipeline(args.run_id)
    
    # Print summary
    overall_passed = all(result.get('passed', True) for result in results.values())
    print(f"\nValidation Summary for run {args.run_id}:")
    print(f"Overall Status: {'PASSED' if overall_passed else 'FAILED'}")
    print("\nDetailed Results:")
    
    for validation_type, result in results.items():
        status = 'PASSED' if result.get('passed', True) else 'FAILED'
        print(f"  {validation_type.title()}: {status}")
        
        if not result.get('passed', True) and 'failed_checks' in result:
            for check in result['failed_checks']:
                print(f"    - {check}")

if __name__ == "__main__":
    main()
