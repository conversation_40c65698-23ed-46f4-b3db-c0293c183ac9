#!/usr/bin/env python3
"""
MLflow Configuration and Setup
Centralized configuration for MLflow tracking, model registry, and experiments
"""

import os
import sys
import logging
from typing import Dict, Any, Optional, List
import mlflow
from mlflow.tracking import MlflowClient
from mlflow.entities import Experiment
import boto3
from urllib.parse import urlparse
import yaml

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MLflowConfig:
    """MLflow configuration and management"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config = self.load_config(config_path)
        self.client = None
        self.setup_mlflow()
    
    def load_config(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """Load MLflow configuration"""
        default_config = {
            'tracking_uri': os.getenv('MLFLOW_TRACKING_URI', 'http://localhost:5000'),
            'artifact_root': os.getenv('MLFLOW_ARTIFACT_ROOT', './mlruns'),
            'default_experiment': 'fraud-detection',
            'model_registry': {
                'enabled': True,
                'default_stage': 'None'
            },
            'backend_store': {
                'type': 'sqlite',  # sqlite, postgresql, mysql
                'uri': os.getenv('MLFLOW_BACKEND_STORE_URI', 'sqlite:///mlflow.db')
            },
            'artifact_store': {
                'type': 'local',  # local, s3, azure, gcs
                'config': {}
            },
            'authentication': {
                'enabled': False,
                'username': os.getenv('MLFLOW_USERNAME'),
                'password': os.getenv('MLFLOW_PASSWORD')
            },
            'experiments': {
                'fraud-detection': {
                    'description': 'Fraud detection model experiments',
                    'tags': {
                        'project': 'fraudshield',
                        'team': 'ml-engineering',
                        'domain': 'fraud-detection'
                    }
                },
                'fraud-detection-dev': {
                    'description': 'Development experiments for fraud detection',
                    'tags': {
                        'project': 'fraudshield',
                        'team': 'ml-engineering',
                        'domain': 'fraud-detection',
                        'environment': 'development'
                    }
                },
                'fraud-detection-staging': {
                    'description': 'Staging experiments for fraud detection',
                    'tags': {
                        'project': 'fraudshield',
                        'team': 'ml-engineering',
                        'domain': 'fraud-detection',
                        'environment': 'staging'
                    }
                }
            },
            'registered_models': {
                'fraud-detection': {
                    'description': 'Production fraud detection model',
                    'tags': {
                        'model_type': 'classification',
                        'use_case': 'fraud_detection',
                        'framework': 'scikit-learn'
                    }
                }
            }
        }
        
        if config_path and os.path.exists(config_path):
            with open(config_path, 'r') as f:
                file_config = yaml.safe_load(f)
                default_config.update(file_config)
        
        return default_config
    
    def setup_mlflow(self):
        """Setup MLflow tracking and configuration"""
        logger.info("Setting up MLflow configuration...")
        
        # Set tracking URI
        mlflow.set_tracking_uri(self.config['tracking_uri'])
        logger.info(f"MLflow tracking URI: {self.config['tracking_uri']}")
        
        # Initialize client
        self.client = MlflowClient()
        
        # Setup artifact store
        self.setup_artifact_store()
        
        # Setup experiments
        self.setup_experiments()
        
        # Setup registered models
        self.setup_registered_models()
        
        logger.info("MLflow setup completed")
    
    def setup_artifact_store(self):
        """Setup artifact store configuration"""
        artifact_config = self.config['artifact_store']
        
        if artifact_config['type'] == 's3':
            self.setup_s3_artifact_store(artifact_config['config'])
        elif artifact_config['type'] == 'azure':
            self.setup_azure_artifact_store(artifact_config['config'])
        elif artifact_config['type'] == 'gcs':
            self.setup_gcs_artifact_store(artifact_config['config'])
        else:
            logger.info("Using local artifact store")
    
    def setup_s3_artifact_store(self, s3_config: Dict[str, Any]):
        """Setup S3 artifact store"""
        logger.info("Configuring S3 artifact store...")
        
        # Set S3 environment variables
        if 'aws_access_key_id' in s3_config:
            os.environ['AWS_ACCESS_KEY_ID'] = s3_config['aws_access_key_id']
        
        if 'aws_secret_access_key' in s3_config:
            os.environ['AWS_SECRET_ACCESS_KEY'] = s3_config['aws_secret_access_key']
        
        if 'aws_default_region' in s3_config:
            os.environ['AWS_DEFAULT_REGION'] = s3_config['aws_default_region']
        
        # Set MLflow S3 endpoint URL if specified
        if 'endpoint_url' in s3_config:
            os.environ['MLFLOW_S3_ENDPOINT_URL'] = s3_config['endpoint_url']
        
        logger.info("S3 artifact store configured")
    
    def setup_azure_artifact_store(self, azure_config: Dict[str, Any]):
        """Setup Azure artifact store"""
        logger.info("Configuring Azure artifact store...")
        
        # Set Azure environment variables
        if 'account_name' in azure_config:
            os.environ['AZURE_STORAGE_ACCOUNT'] = azure_config['account_name']
        
        if 'account_key' in azure_config:
            os.environ['AZURE_STORAGE_KEY'] = azure_config['account_key']
        
        if 'connection_string' in azure_config:
            os.environ['AZURE_STORAGE_CONNECTION_STRING'] = azure_config['connection_string']
        
        logger.info("Azure artifact store configured")
    
    def setup_gcs_artifact_store(self, gcs_config: Dict[str, Any]):
        """Setup Google Cloud Storage artifact store"""
        logger.info("Configuring GCS artifact store...")
        
        # Set GCS environment variables
        if 'credentials_path' in gcs_config:
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = gcs_config['credentials_path']
        
        logger.info("GCS artifact store configured")
    
    def setup_experiments(self):
        """Setup MLflow experiments"""
        logger.info("Setting up MLflow experiments...")
        
        for exp_name, exp_config in self.config['experiments'].items():
            try:
                # Check if experiment exists
                experiment = self.client.get_experiment_by_name(exp_name)
                
                if experiment is None:
                    # Create experiment
                    experiment_id = self.client.create_experiment(
                        name=exp_name,
                        artifact_location=exp_config.get('artifact_location'),
                        tags=exp_config.get('tags', {})
                    )
                    logger.info(f"Created experiment: {exp_name} (ID: {experiment_id})")
                else:
                    # Update experiment tags if needed
                    current_tags = experiment.tags or {}
                    new_tags = exp_config.get('tags', {})
                    
                    for tag_key, tag_value in new_tags.items():
                        if tag_key not in current_tags or current_tags[tag_key] != tag_value:
                            self.client.set_experiment_tag(experiment.experiment_id, tag_key, tag_value)
                    
                    logger.info(f"Experiment already exists: {exp_name}")
                
            except Exception as e:
                logger.error(f"Error setting up experiment {exp_name}: {e}")
    
    def setup_registered_models(self):
        """Setup registered models"""
        logger.info("Setting up registered models...")
        
        for model_name, model_config in self.config['registered_models'].items():
            try:
                # Check if model exists
                try:
                    registered_model = self.client.get_registered_model(model_name)
                    logger.info(f"Registered model already exists: {model_name}")
                    
                    # Update tags if needed
                    current_tags = registered_model.tags or {}
                    new_tags = model_config.get('tags', {})
                    
                    for tag_key, tag_value in new_tags.items():
                        if tag_key not in current_tags or current_tags[tag_key] != tag_value:
                            self.client.set_registered_model_tag(model_name, tag_key, tag_value)
                
                except mlflow.exceptions.RestException:
                    # Model doesn't exist, create it
                    registered_model = self.client.create_registered_model(
                        name=model_name,
                        description=model_config.get('description'),
                        tags=model_config.get('tags', {})
                    )
                    logger.info(f"Created registered model: {model_name}")
                
            except Exception as e:
                logger.error(f"Error setting up registered model {model_name}: {e}")
    
    def get_experiment_id(self, experiment_name: str) -> Optional[str]:
        """Get experiment ID by name"""
        try:
            experiment = self.client.get_experiment_by_name(experiment_name)
            return experiment.experiment_id if experiment else None
        except Exception as e:
            logger.error(f"Error getting experiment ID for {experiment_name}: {e}")
            return None
    
    def create_run(self, experiment_name: str, run_name: Optional[str] = None, 
                  tags: Optional[Dict[str, str]] = None) -> str:
        """Create a new MLflow run"""
        experiment_id = self.get_experiment_id(experiment_name)
        
        if experiment_id is None:
            raise ValueError(f"Experiment {experiment_name} not found")
        
        run = self.client.create_run(
            experiment_id=experiment_id,
            run_name=run_name,
            tags=tags
        )
        
        return run.info.run_id
    
    def log_model_metrics(self, run_id: str, metrics: Dict[str, float]):
        """Log model metrics to MLflow"""
        for metric_name, metric_value in metrics.items():
            self.client.log_metric(run_id, metric_name, metric_value)
    
    def log_model_params(self, run_id: str, params: Dict[str, Any]):
        """Log model parameters to MLflow"""
        for param_name, param_value in params.items():
            self.client.log_param(run_id, param_name, str(param_value))
    
    def log_model_artifacts(self, run_id: str, artifacts: Dict[str, str]):
        """Log model artifacts to MLflow"""
        for artifact_name, artifact_path in artifacts.items():
            self.client.log_artifact(run_id, artifact_path, artifact_name)
    
    def register_model(self, run_id: str, model_name: str, 
                      model_path: str = "model") -> str:
        """Register model from run"""
        model_uri = f"runs:/{run_id}/{model_path}"
        
        model_version = self.client.create_model_version(
            name=model_name,
            source=model_uri,
            run_id=run_id
        )
        
        logger.info(f"Registered model {model_name} version {model_version.version}")
        return model_version.version
    
    def transition_model_stage(self, model_name: str, version: str, 
                             stage: str, archive_existing: bool = True):
        """Transition model to specified stage"""
        self.client.transition_model_version_stage(
            name=model_name,
            version=version,
            stage=stage,
            archive_existing_versions=archive_existing
        )
        
        logger.info(f"Transitioned {model_name} v{version} to {stage}")
    
    def get_latest_model_version(self, model_name: str, 
                               stage: str = "Production") -> Optional[str]:
        """Get latest model version in specified stage"""
        try:
            latest_versions = self.client.get_latest_versions(model_name, stages=[stage])
            return latest_versions[0].version if latest_versions else None
        except Exception as e:
            logger.error(f"Error getting latest model version: {e}")
            return None
    
    def compare_models(self, model_name: str, version1: str, version2: str) -> Dict[str, Any]:
        """Compare two model versions"""
        try:
            # Get model version details
            mv1 = self.client.get_model_version(model_name, version1)
            mv2 = self.client.get_model_version(model_name, version2)
            
            # Get run details
            run1 = self.client.get_run(mv1.run_id)
            run2 = self.client.get_run(mv2.run_id)
            
            comparison = {
                'model_name': model_name,
                'version1': {
                    'version': version1,
                    'run_id': mv1.run_id,
                    'metrics': run1.data.metrics,
                    'params': run1.data.params,
                    'stage': mv1.current_stage,
                    'creation_timestamp': mv1.creation_timestamp
                },
                'version2': {
                    'version': version2,
                    'run_id': mv2.run_id,
                    'metrics': run2.data.metrics,
                    'params': run2.data.params,
                    'stage': mv2.current_stage,
                    'creation_timestamp': mv2.creation_timestamp
                }
            }
            
            return comparison
            
        except Exception as e:
            logger.error(f"Error comparing models: {e}")
            return {}
    
    def cleanup_old_runs(self, experiment_name: str, keep_last_n: int = 10):
        """Cleanup old runs in experiment"""
        try:
            experiment = self.client.get_experiment_by_name(experiment_name)
            if experiment is None:
                logger.warning(f"Experiment {experiment_name} not found")
                return
            
            # Get all runs
            runs = self.client.search_runs(
                experiment_ids=[experiment.experiment_id],
                order_by=["start_time DESC"]
            )
            
            # Delete old runs
            if len(runs) > keep_last_n:
                runs_to_delete = runs[keep_last_n:]
                
                for run in runs_to_delete:
                    self.client.delete_run(run.info.run_id)
                    logger.info(f"Deleted run {run.info.run_id}")
                
                logger.info(f"Cleaned up {len(runs_to_delete)} old runs")
            
        except Exception as e:
            logger.error(f"Error cleaning up old runs: {e}")
    
    def export_model_metadata(self, model_name: str, version: str) -> Dict[str, Any]:
        """Export model metadata"""
        try:
            model_version = self.client.get_model_version(model_name, version)
            run = self.client.get_run(model_version.run_id)
            
            metadata = {
                'model_name': model_name,
                'version': version,
                'run_id': model_version.run_id,
                'stage': model_version.current_stage,
                'creation_timestamp': model_version.creation_timestamp,
                'last_updated_timestamp': model_version.last_updated_timestamp,
                'description': model_version.description,
                'tags': model_version.tags,
                'metrics': run.data.metrics,
                'params': run.data.params,
                'artifacts': [artifact.path for artifact in self.client.list_artifacts(model_version.run_id)]
            }
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error exporting model metadata: {e}")
            return {}

def create_mlflow_config(config_path: Optional[str] = None) -> MLflowConfig:
    """Factory function to create MLflow configuration"""
    return MLflowConfig(config_path)

def main():
    """Main function for testing MLflow configuration"""
    import argparse
    
    parser = argparse.ArgumentParser(description='MLflow configuration setup')
    parser.add_argument('--config', type=str, help='Path to MLflow configuration file')
    parser.add_argument('--setup', action='store_true', help='Setup MLflow configuration')
    parser.add_argument('--test', action='store_true', help='Test MLflow connection')
    
    args = parser.parse_args()
    
    # Create MLflow configuration
    mlflow_config = create_mlflow_config(args.config)
    
    if args.setup:
        logger.info("MLflow configuration setup completed")
    
    if args.test:
        # Test MLflow connection
        try:
            experiments = mlflow_config.client.search_experiments()
            logger.info(f"Successfully connected to MLflow. Found {len(experiments)} experiments.")
            
            for exp in experiments:
                logger.info(f"  - {exp.name} (ID: {exp.experiment_id})")
        
        except Exception as e:
            logger.error(f"Failed to connect to MLflow: {e}")

if __name__ == "__main__":
    main()
