"""
Model service for fraud detection
"""

import asyncio
import time
import os
from typing import Dict, Any, Optional, List
import pandas as pd
import numpy as np
import joblib
import mlflow
from datetime import datetime

from ..core.config import settings
from ..core.logging import get_logger, MLMetricsLogger
from ..core.exceptions import ModelLoadException, PredictionException, ModelValidationException
from ..models.prediction import PredictionResponse, ModelInfo, RiskLevel, ModelExplanation
from ..ml.algorithms.ensemble_models import EnsembleModelManager
from ..ml.algorithms.neural_networks import FraudDetectionNN
from ..ml.algorithms.anomaly_detection import AnomalyDetectionEnsemble
from ..ml.feature_engineering.feature_extractor import FraudFeatureExtractor

logger = get_logger(__name__)
metrics_logger = MLMetricsLogger()


class ModelService:
    """Main model service for fraud detection predictions"""
    
    def __init__(self):
        self.models = {}
        self.active_model = None
        self.model_version = settings.MODEL_VERSION
        self.model_type = settings.MODEL_TYPE
        self.feature_extractor = FraudFeatureExtractor()
        self.is_initialized = False
        self.model_metadata = {}
        
        # Performance tracking
        self.prediction_count = 0
        self.total_prediction_time = 0.0
        self.last_model_load_time = None
    
    async def initialize(self):
        """Initialize the model service"""
        logger.info("Initializing model service", model_type=self.model_type)
        
        try:
            # Set up MLflow
            mlflow.set_tracking_uri(settings.MLFLOW_TRACKING_URI)
            
            # Load models based on configuration
            await self._load_models()
            
            # Validate models
            await self._validate_models()
            
            self.is_initialized = True
            self.last_model_load_time = datetime.utcnow()
            
            logger.info(
                "Model service initialized successfully",
                active_model=self.active_model,
                model_count=len(self.models)
            )
            
        except Exception as e:
            logger.error("Failed to initialize model service", error=str(e))
            raise ModelLoadException(f"Model service initialization failed: {str(e)}")
    
    async def _load_models(self):
        """Load fraud detection models"""
        
        if self.model_type == "ensemble" or self.model_type == "all":
            await self._load_ensemble_model()
        
        if self.model_type == "neural" or self.model_type == "all":
            await self._load_neural_model()
        
        if self.model_type == "anomaly" or self.model_type == "all":
            await self._load_anomaly_model()
        
        # Load legacy model if exists
        if os.path.exists(settings.MODEL_PATH):
            await self._load_legacy_model()
        
        # Set active model
        if self.models:
            # Priority: ensemble > neural > anomaly > legacy
            if "ensemble" in self.models:
                self.active_model = "ensemble"
            elif "neural" in self.models:
                self.active_model = "neural"
            elif "anomaly" in self.models:
                self.active_model = "anomaly"
            elif "legacy" in self.models:
                self.active_model = "legacy"
            else:
                raise ModelLoadException("No valid models loaded")
        else:
            raise ModelLoadException("No models found to load")
    
    async def _load_ensemble_model(self):
        """Load ensemble model"""
        try:
            ensemble_manager = EnsembleModelManager()
            
            # Try to load from MLflow first
            try:
                model_uri = f"models:/{settings.MLFLOW_EXPERIMENT_NAME}_ensemble/latest"
                ensemble_manager = mlflow.sklearn.load_model(model_uri)
                logger.info("Loaded ensemble model from MLflow")
            except:
                # Try to load from local directory
                ensemble_dir = "/models/ensemble_models"
                if os.path.exists(ensemble_dir):
                    ensemble_manager.load_models(ensemble_dir)
                    logger.info("Loaded ensemble model from local directory")
                else:
                    logger.warning("No ensemble model found")
                    return
            
            self.models["ensemble"] = ensemble_manager
            self.model_metadata["ensemble"] = {
                "type": "ensemble",
                "loaded_at": datetime.utcnow(),
                "model_count": len(ensemble_manager.models) if hasattr(ensemble_manager, 'models') else 0
            }
            
        except Exception as e:
            logger.error("Failed to load ensemble model", error=str(e))
    
    async def _load_neural_model(self):
        """Load neural network model"""
        try:
            nn_model = FraudDetectionNN()
            
            # Try to load from MLflow first
            try:
                model_uri = f"models:/{settings.MLFLOW_EXPERIMENT_NAME}_neural/latest"
                nn_model.model = mlflow.tensorflow.load_model(model_uri)
                # Load scaler and metadata separately
                logger.info("Loaded neural model from MLflow")
            except:
                # Try to load from local files
                model_path = "/models/neural_models/fraud_nn_model.h5"
                scaler_path = "/models/neural_models/nn_scaler.pkl"
                
                if os.path.exists(model_path) and os.path.exists(scaler_path):
                    nn_model.load_model(model_path, scaler_path)
                    logger.info("Loaded neural model from local files")
                else:
                    logger.warning("No neural model found")
                    return
            
            self.models["neural"] = nn_model
            self.model_metadata["neural"] = {
                "type": "neural_network",
                "loaded_at": datetime.utcnow(),
                "is_trained": nn_model.is_trained
            }
            
        except Exception as e:
            logger.error("Failed to load neural model", error=str(e))
    
    async def _load_anomaly_model(self):
        """Load anomaly detection model"""
        try:
            anomaly_ensemble = AnomalyDetectionEnsemble()
            
            # Try to load from local directory
            anomaly_dir = "/models/anomaly_models"
            if os.path.exists(anomaly_dir):
                anomaly_ensemble.load_models(anomaly_dir)
                logger.info("Loaded anomaly model from local directory")
            else:
                logger.warning("No anomaly model found")
                return
            
            self.models["anomaly"] = anomaly_ensemble
            self.model_metadata["anomaly"] = {
                "type": "anomaly_detection",
                "loaded_at": datetime.utcnow(),
                "model_count": len(anomaly_ensemble.models)
            }
            
        except Exception as e:
            logger.error("Failed to load anomaly model", error=str(e))
    
    async def _load_legacy_model(self):
        """Load legacy pickle model"""
        try:
            legacy_model = joblib.load(settings.MODEL_PATH)
            
            self.models["legacy"] = legacy_model
            self.model_metadata["legacy"] = {
                "type": "legacy_sklearn",
                "loaded_at": datetime.utcnow(),
                "model_path": settings.MODEL_PATH
            }
            
            logger.info("Loaded legacy model", model_path=settings.MODEL_PATH)
            
        except Exception as e:
            logger.error("Failed to load legacy model", error=str(e))
    
    async def _validate_models(self):
        """Validate loaded models"""
        for model_name, model in self.models.items():
            try:
                # Create dummy data for validation
                dummy_features = self._create_dummy_features()
                
                if model_name == "legacy":
                    # Legacy model expects specific feature format
                    legacy_features = self._prepare_legacy_features(dummy_features)
                    prediction = model.predict_proba([legacy_features])
                elif hasattr(model, 'predict_proba'):
                    prediction = model.predict_proba(pd.DataFrame([dummy_features]))
                else:
                    prediction = model.predict(pd.DataFrame([dummy_features]))
                
                logger.info(f"Model {model_name} validation successful")
                
            except Exception as e:
                logger.error(f"Model {model_name} validation failed", error=str(e))
                # Remove invalid model
                del self.models[model_name]
                if model_name in self.model_metadata:
                    del self.model_metadata[model_name]
    
    def _create_dummy_features(self) -> Dict[str, float]:
        """Create dummy features for model validation"""
        feature_names = self.feature_extractor.get_feature_names()
        return {name: 0.0 for name in feature_names}
    
    def _prepare_legacy_features(self, features: Dict[str, float]) -> List[float]:
        """Prepare features for legacy model"""
        # Legacy model expects specific feature order
        legacy_feature_order = [
            'amount', 'oldbalanceOrg', 'newbalanceOrig', 'oldbalanceDest', 'newbalanceDest',
            'merchantFlag', 'type_CASH_OUT', 'type_DEBIT', 'type_PAYMENT', 'type_TRANSFER'
        ]
        
        legacy_features = []
        for feature_name in legacy_feature_order:
            if feature_name == 'merchantFlag':
                # Derive merchant flag from destination type
                legacy_features.append(features.get('dest_is_merchant', 0.0))
            else:
                legacy_features.append(features.get(feature_name, 0.0))
        
        return legacy_features
    
    async def predict(self, features: Dict[str, float]) -> PredictionResponse:
        """Make fraud prediction"""
        if not self.is_initialized:
            raise PredictionException("Model service not initialized")
        
        if not self.models or not self.active_model:
            raise PredictionException("No active model available")
        
        start_time = time.time()
        
        try:
            model = self.models[self.active_model]
            
            # Prepare features
            if self.active_model == "legacy":
                model_features = self._prepare_legacy_features(features)
                fraud_score = float(model.predict_proba([model_features])[0][1])
            else:
                feature_df = pd.DataFrame([features])
                if hasattr(model, 'predict_proba'):
                    fraud_score = float(model.predict_proba(feature_df)[0])
                else:
                    # For anomaly detection models
                    fraud_score = float(model.predict_proba(feature_df)[0])
            
            # Determine fraud classification
            is_fraudulent = fraud_score >= settings.FRAUD_THRESHOLD
            
            # Calculate confidence (simplified)
            confidence = min(fraud_score * 2, 1.0) if is_fraudulent else min((1 - fraud_score) * 2, 1.0)
            
            # Determine risk level
            if fraud_score >= settings.HIGH_RISK_THRESHOLD:
                risk_level = RiskLevel.CRITICAL
            elif fraud_score >= 0.6:
                risk_level = RiskLevel.HIGH
            elif fraud_score >= 0.3:
                risk_level = RiskLevel.MEDIUM
            else:
                risk_level = RiskLevel.LOW
            
            processing_time_ms = (time.time() - start_time) * 1000
            
            # Update metrics
            self.prediction_count += 1
            self.total_prediction_time += processing_time_ms
            
            response = PredictionResponse(
                transaction_id="",  # Will be set by caller
                fraud_score=fraud_score,
                is_fraudulent=is_fraudulent,
                risk_level=risk_level,
                confidence=confidence,
                model_version=self.model_version,
                processing_time_ms=processing_time_ms
            )
            
            return response
            
        except Exception as e:
            logger.error("Prediction failed", error=str(e), active_model=self.active_model)
            raise PredictionException(f"Prediction failed: {str(e)}", self.active_model)
    
    async def get_model_info(self) -> Dict[str, Any]:
        """Get information about loaded models"""
        if not self.is_initialized:
            return {"status": "not_initialized"}
        
        model_info = {
            "active_model": self.active_model,
            "model_version": self.model_version,
            "models_loaded": list(self.models.keys()),
            "model_metadata": self.model_metadata,
            "performance_stats": {
                "prediction_count": self.prediction_count,
                "avg_prediction_time_ms": (
                    self.total_prediction_time / self.prediction_count
                    if self.prediction_count > 0 else 0
                ),
                "last_model_load_time": self.last_model_load_time.isoformat() if self.last_model_load_time else None
            },
            "feature_count": len(self.feature_extractor.get_feature_names()),
            "is_initialized": self.is_initialized
        }
        
        return model_info
    
    async def get_model_version(self) -> str:
        """Get current model version"""
        return self.model_version
    
    async def is_ready(self) -> bool:
        """Check if model service is ready"""
        return self.is_initialized and bool(self.models) and self.active_model is not None
    
    async def cleanup(self):
        """Cleanup model service"""
        logger.info("Cleaning up model service")
        
        # Clear models
        self.models.clear()
        self.model_metadata.clear()
        
        # Reset state
        self.is_initialized = False
        self.active_model = None
        
        logger.info("Model service cleanup completed")
