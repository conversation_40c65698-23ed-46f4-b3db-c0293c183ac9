"""
Model utilities for fraud detection
"""

import os
import pickle
import joblib
import json
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime
import numpy as np
import pandas as pd
import mlflow
import mlflow.sklearn
import mlflow.tensorflow

from ...core.logging import get_logger
from ...core.exceptions import ModelLoadException, ModelValidationException

logger = get_logger(__name__)


class ModelVersionManager:
    """Manage model versions and metadata"""
    
    def __init__(self, base_path: str = "/models"):
        self.base_path = base_path
        self.metadata_file = os.path.join(base_path, "model_metadata.json")
        self.metadata = self._load_metadata()
    
    def _load_metadata(self) -> Dict[str, Any]:
        """Load model metadata"""
        if os.path.exists(self.metadata_file):
            try:
                with open(self.metadata_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load metadata: {e}")
        
        return {
            "models": {},
            "active_versions": {},
            "last_updated": None
        }
    
    def _save_metadata(self):
        """Save model metadata"""
        try:
            os.makedirs(os.path.dirname(self.metadata_file), exist_ok=True)
            self.metadata["last_updated"] = datetime.now().isoformat()
            
            with open(self.metadata_file, 'w') as f:
                json.dump(self.metadata, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save metadata: {e}")
    
    def register_model(
        self,
        model_name: str,
        model_path: str,
        model_type: str,
        version: str,
        metrics: Dict[str, float] = None,
        metadata: Dict[str, Any] = None
    ):
        """Register a new model version"""
        
        model_info = {
            "model_path": model_path,
            "model_type": model_type,
            "version": version,
            "registered_at": datetime.now().isoformat(),
            "metrics": metrics or {},
            "metadata": metadata or {}
        }
        
        if model_name not in self.metadata["models"]:
            self.metadata["models"][model_name] = {}
        
        self.metadata["models"][model_name][version] = model_info
        
        # Set as active if it's the first version or has better performance
        current_active = self.metadata["active_versions"].get(model_name)
        if not current_active or self._is_better_model(model_name, version, current_active):
            self.metadata["active_versions"][model_name] = version
        
        self._save_metadata()
        
        logger.info(
            f"Model registered: {model_name} v{version}",
            model_path=model_path,
            is_active=self.metadata["active_versions"][model_name] == version
        )
    
    def _is_better_model(self, model_name: str, new_version: str, current_version: str) -> bool:
        """Determine if new model version is better than current"""
        try:
            new_metrics = self.metadata["models"][model_name][new_version]["metrics"]
            current_metrics = self.metadata["models"][model_name][current_version]["metrics"]
            
            # Compare based on AUC-ROC, then F1 score
            new_auc = new_metrics.get("auc_roc", 0)
            current_auc = current_metrics.get("auc_roc", 0)
            
            if new_auc > current_auc:
                return True
            elif new_auc == current_auc:
                new_f1 = new_metrics.get("f1_score", 0)
                current_f1 = current_metrics.get("f1_score", 0)
                return new_f1 > current_f1
            
            return False
        except Exception:
            return False
    
    def get_active_model_info(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Get information about the active model version"""
        active_version = self.metadata["active_versions"].get(model_name)
        if active_version and model_name in self.metadata["models"]:
            return self.metadata["models"][model_name][active_version]
        return None
    
    def list_models(self) -> Dict[str, Any]:
        """List all registered models"""
        return self.metadata
    
    def set_active_version(self, model_name: str, version: str):
        """Set active version for a model"""
        if model_name in self.metadata["models"] and version in self.metadata["models"][model_name]:
            self.metadata["active_versions"][model_name] = version
            self._save_metadata()
            logger.info(f"Active version set: {model_name} v{version}")
        else:
            raise ValueError(f"Model {model_name} version {version} not found")


class ModelLoader:
    """Load and validate models"""
    
    @staticmethod
    def load_sklearn_model(model_path: str) -> Any:
        """Load scikit-learn model"""
        try:
            if model_path.endswith('.pkl'):
                return joblib.load(model_path)
            elif model_path.endswith('.pickle'):
                with open(model_path, 'rb') as f:
                    return pickle.load(f)
            else:
                raise ValueError(f"Unsupported sklearn model format: {model_path}")
        except Exception as e:
            raise ModelLoadException(f"Failed to load sklearn model: {e}", model_path)
    
    @staticmethod
    def load_tensorflow_model(model_path: str) -> Any:
        """Load TensorFlow/Keras model"""
        try:
            import tensorflow as tf
            return tf.keras.models.load_model(model_path)
        except Exception as e:
            raise ModelLoadException(f"Failed to load TensorFlow model: {e}", model_path)
    
    @staticmethod
    def load_mlflow_model(model_uri: str) -> Any:
        """Load model from MLflow"""
        try:
            return mlflow.sklearn.load_model(model_uri)
        except Exception:
            try:
                return mlflow.tensorflow.load_model(model_uri)
            except Exception as e:
                raise ModelLoadException(f"Failed to load MLflow model: {e}", model_uri)
    
    @staticmethod
    def validate_model(model: Any, sample_input: np.ndarray) -> bool:
        """Validate that model can make predictions"""
        try:
            if hasattr(model, 'predict'):
                prediction = model.predict(sample_input)
                return prediction is not None
            elif hasattr(model, 'predict_proba'):
                prediction = model.predict_proba(sample_input)
                return prediction is not None
            else:
                return False
        except Exception as e:
            logger.error(f"Model validation failed: {e}")
            return False


class FeatureValidator:
    """Validate feature consistency"""
    
    def __init__(self, expected_features: List[str]):
        self.expected_features = set(expected_features)
    
    def validate_features(self, features: Dict[str, float]) -> Tuple[bool, List[str]]:
        """Validate feature dictionary"""
        provided_features = set(features.keys())
        
        missing_features = self.expected_features - provided_features
        extra_features = provided_features - self.expected_features
        
        issues = []
        
        if missing_features:
            issues.append(f"Missing features: {list(missing_features)}")
        
        if extra_features:
            issues.append(f"Unexpected features: {list(extra_features)}")
        
        # Check for invalid values
        invalid_features = []
        for name, value in features.items():
            if not isinstance(value, (int, float)) or np.isnan(value) or np.isinf(value):
                invalid_features.append(name)
        
        if invalid_features:
            issues.append(f"Invalid feature values: {invalid_features}")
        
        is_valid = len(issues) == 0
        return is_valid, issues
    
    def fix_features(self, features: Dict[str, float]) -> Dict[str, float]:
        """Fix common feature issues"""
        fixed_features = {}
        
        # Add missing features with default values
        for feature_name in self.expected_features:
            if feature_name in features:
                value = features[feature_name]
                
                # Fix invalid values
                if np.isnan(value) or np.isinf(value):
                    fixed_features[feature_name] = 0.0
                else:
                    fixed_features[feature_name] = float(value)
            else:
                # Set default value for missing features
                fixed_features[feature_name] = 0.0
        
        return fixed_features


class ModelPerformanceTracker:
    """Track model performance over time"""
    
    def __init__(self, tracking_file: str = "/models/performance_tracking.json"):
        self.tracking_file = tracking_file
        self.performance_data = self._load_performance_data()
    
    def _load_performance_data(self) -> Dict[str, List[Dict[str, Any]]]:
        """Load performance tracking data"""
        if os.path.exists(self.tracking_file):
            try:
                with open(self.tracking_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load performance data: {e}")
        
        return {}
    
    def _save_performance_data(self):
        """Save performance tracking data"""
        try:
            os.makedirs(os.path.dirname(self.tracking_file), exist_ok=True)
            with open(self.tracking_file, 'w') as f:
                json.dump(self.performance_data, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Failed to save performance data: {e}")
    
    def record_prediction(
        self,
        model_name: str,
        prediction_score: float,
        actual_label: Optional[int] = None,
        processing_time: float = None
    ):
        """Record a single prediction"""
        
        if model_name not in self.performance_data:
            self.performance_data[model_name] = []
        
        record = {
            "timestamp": datetime.now().isoformat(),
            "prediction_score": prediction_score,
            "processing_time": processing_time
        }
        
        if actual_label is not None:
            record["actual_label"] = actual_label
        
        self.performance_data[model_name].append(record)
        
        # Keep only recent records (last 10000)
        if len(self.performance_data[model_name]) > 10000:
            self.performance_data[model_name] = self.performance_data[model_name][-10000:]
        
        self._save_performance_data()
    
    def get_performance_summary(self, model_name: str, days: int = 7) -> Dict[str, Any]:
        """Get performance summary for recent period"""
        
        if model_name not in self.performance_data:
            return {}
        
        from datetime import timedelta
        cutoff_date = datetime.now() - timedelta(days=days)
        
        recent_records = []
        for record in self.performance_data[model_name]:
            try:
                record_date = datetime.fromisoformat(record["timestamp"])
                if record_date >= cutoff_date:
                    recent_records.append(record)
            except Exception:
                continue
        
        if not recent_records:
            return {}
        
        # Calculate summary statistics
        scores = [r["prediction_score"] for r in recent_records]
        processing_times = [r["processing_time"] for r in recent_records if r.get("processing_time")]
        
        summary = {
            "total_predictions": len(recent_records),
            "avg_score": np.mean(scores),
            "score_std": np.std(scores),
            "score_percentiles": {
                "p50": np.percentile(scores, 50),
                "p90": np.percentile(scores, 90),
                "p95": np.percentile(scores, 95),
                "p99": np.percentile(scores, 99)
            }
        }
        
        if processing_times:
            summary["avg_processing_time"] = np.mean(processing_times)
            summary["processing_time_p95"] = np.percentile(processing_times, 95)
        
        # Calculate accuracy if labels are available
        labeled_records = [r for r in recent_records if "actual_label" in r]
        if labeled_records:
            predictions = [(r["prediction_score"] > 0.5) for r in labeled_records]
            actuals = [r["actual_label"] for r in labeled_records]
            
            correct = sum(p == a for p, a in zip(predictions, actuals))
            summary["accuracy"] = correct / len(labeled_records)
            summary["labeled_samples"] = len(labeled_records)
        
        return summary


def create_model_signature(feature_names: List[str]) -> Dict[str, Any]:
    """Create model signature for MLflow"""
    
    import mlflow.types
    from mlflow.models.signature import infer_signature
    import pandas as pd
    
    # Create sample input
    sample_input = pd.DataFrame({name: [0.0] for name in feature_names})
    sample_output = pd.DataFrame({"fraud_score": [0.5]})
    
    signature = infer_signature(sample_input, sample_output)
    
    return {
        "signature": signature,
        "input_schema": signature.inputs.to_dict(),
        "output_schema": signature.outputs.to_dict()
    }
