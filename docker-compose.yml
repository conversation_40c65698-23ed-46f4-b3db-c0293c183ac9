version: '3.8'

services:
  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_ML_SERVICE_URL=http://localhost:8001
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - fraudshield-network

  # Backend API Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=***********************************************/fraudshield
      - REDIS_URL=redis://redis:6379
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - ML_SERVICE_URL=http://ml-service:8001
      - JWT_SECRET_KEY=your-secret-key-change-in-production
      - ENVIRONMENT=development
    volumes:
      - ./backend:/app
      - ./data:/app/data
    depends_on:
      - postgres
      - redis
      - kafka
      - ml-service
    networks:
      - fraudshield-network

  # ML Model Serving Service
  ml-service:
    build:
      context: ./ml-service
      dockerfile: Dockerfile
    ports:
      - "8001:8001"
    environment:
      - MODEL_PATH=/models/fraud_detection_model.pkl
      - FEATURE_STORE_URL=http://feature-store:8002
    volumes:
      - ./ml-service:/app
      - ./data:/app/data
      - ./models:/models
    networks:
      - fraudshield-network

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=fraudshield
      - POSTGRES_USER=fraudshield
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - fraudshield-network

  # Redis for Caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - fraudshield-network

  # Apache Kafka for Message Streaming
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - fraudshield-network

  kafka:
    image: confluentinc/cp-kafka:latest
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    volumes:
      - kafka_data:/var/lib/kafka/data
    networks:
      - fraudshield-network

  # InfluxDB for Time Series Data
  influxdb:
    image: influxdb:2.7-alpine
    ports:
      - "8086:8086"
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=admin
      - DOCKER_INFLUXDB_INIT_PASSWORD=password123
      - DOCKER_INFLUXDB_INIT_ORG=fraudshield
      - DOCKER_INFLUXDB_INIT_BUCKET=transactions
    volumes:
      - influxdb_data:/var/lib/influxdb2
    networks:
      - fraudshield-network

  # Grafana for Monitoring
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - influxdb
      - prometheus
    networks:
      - fraudshield-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus for Metrics Collection
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - fraudshield-network

  # Feature Store Service
  feature-store:
    build:
      context: ./feature-store
      dockerfile: Dockerfile
    ports:
      - "8002:8002"
    environment:
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=***********************************************/fraudshield
      - INFLUXDB_URL=http://influxdb:8086
      - INFLUXDB_TOKEN=fraudshield-token
      - INFLUXDB_ORG=fraudshield
      - INFLUXDB_BUCKET=transactions
    depends_on:
      - redis
      - postgres
      - influxdb
    volumes:
      - ./feature-store:/app
    networks:
      - fraudshield-network

  # Stream Processing Service
  stream-processor:
    build:
      context: ./stream-processor
      dockerfile: Dockerfile
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=***********************************************/fraudshield
      - FEATURE_STORE_URL=http://feature-store:8002
    depends_on:
      - kafka
      - redis
      - postgres
      - feature-store
    volumes:
      - ./stream-processor:/app
    networks:
      - fraudshield-network

  # Data Quality Monitor
  data-quality-monitor:
    build:
      context: ./data-quality-monitor
      dockerfile: Dockerfile
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - DATABASE_URL=***********************************************/fraudshield
      - INFLUXDB_URL=http://influxdb:8086
      - INFLUXDB_TOKEN=fraudshield-token
      - INFLUXDB_ORG=fraudshield
      - INFLUXDB_BUCKET=transactions
    depends_on:
      - kafka
      - postgres
      - influxdb
    volumes:
      - ./data-quality-monitor:/app
    networks:
      - fraudshield-network

volumes:
  postgres_data:
  redis_data:
  kafka_data:
  influxdb_data:
  grafana_data:
  prometheus_data:

networks:
  fraudshield-network:
    driver: bridge
