"""
API dependencies for authentication and authorization
"""

from typing import Optional
from uuid import UUI<PERSON>

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db_session
from app.core.security import verify_token, check_permissions
from app.core.exceptions import AuthenticationException, AuthorizationException
from app.core.logging import get_logger
from app.services.user_service import UserService

logger = get_logger(__name__)
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Get current authenticated user from JWT token
    """
    try:
        # Verify token
        payload = verify_token(credentials.credentials)
        
        if payload.get("type") != "access":
            raise AuthenticationException("Invalid token type")
        
        user_id = payload.get("sub")
        if not user_id:
            raise AuthenticationException("Invalid token")
        
        # Get user from database
        user_service = UserService(db)
        user = await user_service.get_user(UUID(user_id))
        
        if not user:
            raise AuthenticationException("User not found")
        
        if not user.is_active:
            raise AuthenticationException("User account is inactive")
        
        return user
        
    except AuthenticationException:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        logger.error("Authentication failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


def require_permission(permission: str):
    """
    Dependency factory for requiring specific permissions
    """
    async def permission_checker(current_user = Depends(get_current_user)):
        if not check_permissions(current_user.role, permission):
            logger.warning(
                "Permission denied",
                user_id=current_user.id,
                required_permission=permission,
                user_role=current_user.role
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{permission}' required"
            )
        return current_user
    
    return permission_checker


def require_role(role: str):
    """
    Dependency factory for requiring specific role
    """
    async def role_checker(current_user = Depends(get_current_user)):
        if current_user.role != role:
            logger.warning(
                "Role requirement not met",
                user_id=current_user.id,
                required_role=role,
                user_role=current_user.role
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Role '{role}' required"
            )
        return current_user
    
    return role_checker


async def get_optional_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Get current user if authenticated, otherwise return None
    """
    if not credentials:
        return None
    
    try:
        return await get_current_user(credentials, db)
    except HTTPException:
        return None


class RateLimitDependency:
    """
    Rate limiting dependency
    """
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 60):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
    
    async def __call__(self, current_user = Depends(get_current_user)):
        from app.core.security import rate_limiter, RateLimitException
        
        identifier = f"user:{current_user.id}"
        
        if not await rate_limiter.is_allowed(
            identifier,
            self.max_requests,
            self.window_seconds
        ):
            logger.warning(
                "Rate limit exceeded",
                user_id=current_user.id,
                max_requests=self.max_requests,
                window_seconds=self.window_seconds
            )
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded"
            )
        
        return current_user


# Common rate limit dependencies
rate_limit_strict = RateLimitDependency(max_requests=10, window_seconds=60)
rate_limit_moderate = RateLimitDependency(max_requests=50, window_seconds=60)
rate_limit_lenient = RateLimitDependency(max_requests=100, window_seconds=60)


async def validate_api_key(
    api_key: str,
    db: AsyncSession = Depends(get_db_session)
):
    """
    Validate API key for service-to-service authentication
    """
    try:
        # This is a simplified implementation
        # In production, you'd validate against a database of API keys
        from app.core.security import validate_api_key
        
        if not validate_api_key(api_key):
            raise AuthenticationException("Invalid API key")
        
        # Return a service user object or permissions
        return {"type": "service", "permissions": ["read:transactions", "write:transactions"]}
        
    except AuthenticationException:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key"
        )
    except Exception as e:
        logger.error("API key validation failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key validation failed"
        )


def require_admin():
    """
    Dependency for admin-only endpoints
    """
    return require_role("admin")


def require_analyst():
    """
    Dependency for analyst-level access
    """
    async def analyst_checker(current_user = Depends(get_current_user)):
        allowed_roles = ["analyst", "admin"]
        if current_user.role not in allowed_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Analyst role or higher required"
            )
        return current_user
    
    return analyst_checker
