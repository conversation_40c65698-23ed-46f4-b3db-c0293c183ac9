import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { toast } from 'react-hot-toast';

// Types
import type {
  ApiResponse,
  PaginatedResponse,
  AuthTokens,
  LoginCredentials,
  RegisterData,
  User,
  Transaction,
  TransactionRequest,
  FraudResult,
  Alert,
  DashboardStats,
  TransactionStats,
} from '@/types';

// Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api/v1';
const REQUEST_TIMEOUT = 30000; // 30 seconds

class ApiClient {
  private client: AxiosInstance;
  private authToken: string | null = null;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: REQUEST_TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
    this.loadAuthToken();
  }

  private setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        if (this.authToken) {
          config.headers.Authorization = `Bearer ${this.authToken}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;
          
          try {
            await this.refreshToken();
            return this.client(originalRequest);
          } catch (refreshError) {
            this.logout();
            window.location.href = '/login';
            return Promise.reject(refreshError);
          }
        }

        // Handle other errors
        if (error.response?.status >= 500) {
          toast.error('Server error. Please try again later.');
        } else if (error.response?.status === 403) {
          toast.error('You do not have permission to perform this action.');
        } else if (error.response?.status === 404) {
          toast.error('Resource not found.');
        } else if (error.response?.data?.detail) {
          toast.error(error.response.data.detail);
        } else if (error.message) {
          toast.error(error.message);
        }

        return Promise.reject(error);
      }
    );
  }

  private loadAuthToken() {
    const token = localStorage.getItem('access_token');
    if (token) {
      this.authToken = token;
    }
  }

  private saveAuthTokens(tokens: AuthTokens) {
    this.authToken = tokens.access_token;
    localStorage.setItem('access_token', tokens.access_token);
    localStorage.setItem('refresh_token', tokens.refresh_token);
  }

  private clearAuthTokens() {
    this.authToken = null;
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  }

  // Authentication methods
  async login(credentials: LoginCredentials): Promise<AuthTokens> {
    const formData = new FormData();
    formData.append('username', credentials.email);
    formData.append('password', credentials.password);

    const response = await this.client.post<AuthTokens>('/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });

    this.saveAuthTokens(response.data);
    return response.data;
  }

  async register(data: RegisterData): Promise<User> {
    const response = await this.client.post<User>('/auth/register', data);
    return response.data;
  }

  async refreshToken(): Promise<AuthTokens> {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await this.client.post<AuthTokens>('/auth/refresh', {
      refresh_token: refreshToken,
    });

    this.saveAuthTokens(response.data);
    return response.data;
  }

  async getCurrentUser(): Promise<User> {
    const response = await this.client.get<User>('/auth/me');
    return response.data;
  }

  logout() {
    this.clearAuthTokens();
  }

  // Transaction methods
  async scoreTransaction(transaction: TransactionRequest): Promise<FraudResult> {
    const response = await this.client.post<FraudResult>('/transactions/score', transaction);
    return response.data;
  }

  async getTransactions(params?: {
    skip?: number;
    limit?: number;
    type?: string;
    risk_level?: string;
    start_date?: string;
    end_date?: string;
  }): Promise<PaginatedResponse<Transaction>> {
    const response = await this.client.get<PaginatedResponse<Transaction>>('/transactions', {
      params,
    });
    return response.data;
  }

  async getTransaction(id: string): Promise<Transaction> {
    const response = await this.client.get<Transaction>(`/transactions/${id}`);
    return response.data;
  }

  async batchScoreTransactions(transactions: TransactionRequest[]): Promise<FraudResult[]> {
    const response = await this.client.post<{ results: FraudResult[] }>('/transactions/batch', {
      transactions,
    });
    return response.data.results;
  }

  // Alert methods
  async getAlerts(params?: {
    skip?: number;
    limit?: number;
    status?: string;
    severity?: string;
    alert_type?: string;
  }): Promise<PaginatedResponse<Alert>> {
    const response = await this.client.get<PaginatedResponse<Alert>>('/alerts', {
      params,
    });
    return response.data;
  }

  async getAlert(id: string): Promise<Alert> {
    const response = await this.client.get<Alert>(`/alerts/${id}`);
    return response.data;
  }

  async updateAlertStatus(id: string, status: string, notes?: string): Promise<Alert> {
    const response = await this.client.patch<Alert>(`/alerts/${id}/status`, {
      status,
      resolution_notes: notes,
    });
    return response.data;
  }

  async createAlert(data: {
    transaction_id: string;
    alert_type: string;
    severity: string;
    title: string;
    description: string;
  }): Promise<Alert> {
    const response = await this.client.post<Alert>('/alerts', data);
    return response.data;
  }

  // Analytics methods
  async getDashboardStats(period?: string): Promise<DashboardStats> {
    const response = await this.client.get<DashboardStats>('/analytics/dashboard', {
      params: { period },
    });
    return response.data;
  }

  async getTransactionStats(params?: {
    period?: string;
    start_date?: string;
    end_date?: string;
    group_by?: string;
  }): Promise<TransactionStats[]> {
    const response = await this.client.get<TransactionStats[]>('/analytics/transactions', {
      params,
    });
    return response.data;
  }

  async getFraudTrends(params?: {
    period?: string;
    start_date?: string;
    end_date?: string;
  }): Promise<any[]> {
    const response = await this.client.get('/analytics/fraud-trends', {
      params,
    });
    return response.data;
  }

  // User management methods
  async getUsers(params?: {
    skip?: number;
    limit?: number;
    role?: string;
    is_active?: boolean;
  }): Promise<PaginatedResponse<User>> {
    const response = await this.client.get<PaginatedResponse<User>>('/users', {
      params,
    });
    return response.data;
  }

  async createUser(data: {
    email: string;
    password: string;
    full_name: string;
    role: string;
  }): Promise<User> {
    const response = await this.client.post<User>('/users', data);
    return response.data;
  }

  async updateUser(id: string, data: Partial<User>): Promise<User> {
    const response = await this.client.patch<User>(`/users/${id}`, data);
    return response.data;
  }

  async deleteUser(id: string): Promise<void> {
    await this.client.delete(`/users/${id}`);
  }

  // Utility methods
  isAuthenticated(): boolean {
    return !!this.authToken;
  }

  setAuthToken(token: string) {
    this.authToken = token;
    localStorage.setItem('access_token', token);
  }
}

// Create and export singleton instance
export const apiClient = new ApiClient();
export default apiClient;
