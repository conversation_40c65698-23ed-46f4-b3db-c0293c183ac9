"""
Logging configuration for FraudShield
"""

import logging
import sys
from typing import Any, Dict

import structlog
from structlog.stdlib import LoggerFactory


def setup_logging() -> None:
    """Setup structured logging configuration"""
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=logging.INFO,
    )


def get_logger(name: str = None) -> structlog.stdlib.BoundLogger:
    """Get a structured logger instance"""
    return structlog.get_logger(name)


class LoggerMixin:
    """Mixin class to add logging capabilities"""
    
    @property
    def logger(self) -> structlog.stdlib.BoundLogger:
        """Get logger for this class"""
        return get_logger(self.__class__.__name__)


def log_transaction_event(
    logger: structlog.stdlib.BoundLogger,
    event: str,
    transaction_id: str,
    **kwargs: Any
) -> None:
    """Log transaction-related events with consistent format"""
    logger.info(
        event,
        transaction_id=transaction_id,
        **kwargs
    )


def log_fraud_detection(
    logger: structlog.stdlib.BoundLogger,
    transaction_id: str,
    fraud_score: float,
    is_fraudulent: bool,
    risk_level: str,
    processing_time_ms: int,
    **kwargs: Any
) -> None:
    """Log fraud detection results"""
    logger.info(
        "Fraud detection completed",
        transaction_id=transaction_id,
        fraud_score=fraud_score,
        is_fraudulent=is_fraudulent,
        risk_level=risk_level,
        processing_time_ms=processing_time_ms,
        **kwargs
    )


def log_alert_sent(
    logger: structlog.stdlib.BoundLogger,
    alert_type: str,
    transaction_id: str,
    recipient: str,
    channel: str,
    **kwargs: Any
) -> None:
    """Log alert notifications"""
    logger.info(
        "Alert sent",
        alert_type=alert_type,
        transaction_id=transaction_id,
        recipient=recipient,
        channel=channel,
        **kwargs
    )
