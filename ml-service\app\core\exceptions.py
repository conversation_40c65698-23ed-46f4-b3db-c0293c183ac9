"""
Custom exceptions for ML service
"""

from typing import Optional, Dict, Any


class MLServiceException(Exception):
    """Base exception for ML service"""
    
    def __init__(
        self,
        message: str,
        error_code: str = "ML_SERVICE_ERROR",
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class ModelLoadException(MLServiceException):
    """Exception raised when model loading fails"""
    
    def __init__(self, message: str, model_path: str = None):
        super().__init__(
            message=message,
            error_code="MODEL_LOAD_ERROR",
            status_code=503,
            details={"model_path": model_path}
        )


class FeatureExtractionException(MLServiceException):
    """Exception raised when feature extraction fails"""
    
    def __init__(self, message: str, transaction_id: str = None):
        super().__init__(
            message=message,
            error_code="FEATURE_EXTRACTION_ERROR",
            status_code=400,
            details={"transaction_id": transaction_id}
        )


class PredictionException(MLServiceException):
    """Exception raised when prediction fails"""
    
    def __init__(self, message: str, model_name: str = None, transaction_id: str = None):
        super().__init__(
            message=message,
            error_code="PREDICTION_ERROR",
            status_code=500,
            details={"model_name": model_name, "transaction_id": transaction_id}
        )


class ModelValidationException(MLServiceException):
    """Exception raised when model validation fails"""
    
    def __init__(self, message: str, validation_errors: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code="MODEL_VALIDATION_ERROR",
            status_code=400,
            details={"validation_errors": validation_errors or {}}
        )


class DataDriftException(MLServiceException):
    """Exception raised when significant data drift is detected"""
    
    def __init__(self, message: str, drift_metrics: Dict[str, float] = None):
        super().__init__(
            message=message,
            error_code="DATA_DRIFT_ERROR",
            status_code=422,
            details={"drift_metrics": drift_metrics or {}}
        )


class ModelTrainingException(MLServiceException):
    """Exception raised when model training fails"""
    
    def __init__(self, message: str, training_config: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code="MODEL_TRAINING_ERROR",
            status_code=500,
            details={"training_config": training_config or {}}
        )


class FeatureStoreException(MLServiceException):
    """Exception raised when feature store operations fail"""
    
    def __init__(self, message: str, feature_store_url: str = None):
        super().__init__(
            message=message,
            error_code="FEATURE_STORE_ERROR",
            status_code=503,
            details={"feature_store_url": feature_store_url}
        )


class ModelVersionException(MLServiceException):
    """Exception raised when model versioning fails"""
    
    def __init__(self, message: str, model_version: str = None):
        super().__init__(
            message=message,
            error_code="MODEL_VERSION_ERROR",
            status_code=400,
            details={"model_version": model_version}
        )
