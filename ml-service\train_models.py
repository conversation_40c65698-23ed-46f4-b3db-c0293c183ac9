#!/usr/bin/env python3
"""
Comprehensive fraud detection model training script
"""

import os
import sys
import argparse
import json
from datetime import datetime
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.core.config import settings
from app.core.logging import setup_logging, get_logger
from app.ml.training.trainer import FraudModelTrainer

# Setup logging
setup_logging()
logger = get_logger(__name__)


def main():
    """Main training function"""
    parser = argparse.ArgumentParser(description='Train fraud detection models')
    
    parser.add_argument(
        '--data-path',
        type=str,
        default='/data/fraud_detection_samples.csv',
        help='Path to training data CSV file'
    )
    
    parser.add_argument(
        '--model-types',
        nargs='+',
        choices=['ensemble', 'neural', 'anomaly', 'all'],
        default=['all'],
        help='Types of models to train'
    )
    
    parser.add_argument(
        '--resampling-strategy',
        choices=['smote', 'undersampling', 'smoteenn', 'none'],
        default='smote',
        help='Strategy for handling imbalanced data'
    )
    
    parser.add_argument(
        '--hyperparameter-tuning',
        action='store_true',
        default=False,
        help='Enable hyperparameter tuning'
    )
    
    parser.add_argument(
        '--test-size',
        type=float,
        default=0.2,
        help='Test set size (0.0-1.0)'
    )
    
    parser.add_argument(
        '--val-size',
        type=float,
        default=0.2,
        help='Validation set size (0.0-1.0)'
    )
    
    parser.add_argument(
        '--output-dir',
        type=str,
        default='/models',
        help='Output directory for trained models'
    )
    
    parser.add_argument(
        '--experiment-name',
        type=str,
        default=None,
        help='MLflow experiment name'
    )
    
    parser.add_argument(
        '--generate-reports',
        action='store_true',
        default=False,
        help='Generate evaluation reports and plots'
    )
    
    args = parser.parse_args()
    
    # Validate arguments
    if not os.path.exists(args.data_path):
        logger.error(f"Data file not found: {args.data_path}")
        sys.exit(1)
    
    if args.test_size + args.val_size >= 1.0:
        logger.error("Test size + validation size must be < 1.0")
        sys.exit(1)
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Set experiment name
    if args.experiment_name:
        import mlflow
        mlflow.set_experiment(args.experiment_name)
    
    logger.info(
        "Starting fraud detection model training",
        data_path=args.data_path,
        model_types=args.model_types,
        resampling_strategy=args.resampling_strategy,
        hyperparameter_tuning=args.hyperparameter_tuning,
        output_dir=args.output_dir
    )
    
    try:
        # Initialize trainer
        trainer = FraudModelTrainer()
        
        # Determine which models to train
        if 'all' in args.model_types:
            models_to_train = ['ensemble', 'neural', 'anomaly']
        else:
            models_to_train = args.model_types
        
        # Train models
        if len(models_to_train) > 1:
            # Train all models
            results = trainer.train_all_models(
                data_path=args.data_path,
                test_size=args.test_size,
                val_size=args.val_size,
                resampling_strategy=args.resampling_strategy,
                hyperparameter_tuning=args.hyperparameter_tuning
            )
        else:
            # Train individual model
            model_type = models_to_train[0]
            
            # Load and prepare data
            data = trainer.load_training_data(args.data_path)
            X, y = trainer.prepare_features(data)
            
            # Split data
            from sklearn.model_selection import train_test_split
            
            # First split: train + val, test
            X_temp, X_test, y_temp, y_test = train_test_split(
                X, y, test_size=args.test_size, random_state=42, stratify=y
            )
            
            # Second split: train, val
            val_size_adjusted = args.val_size / (1 - args.test_size)
            X_train, X_val, y_train, y_val = train_test_split(
                X_temp, y_temp, test_size=val_size_adjusted, random_state=42, stratify=y_temp
            )
            
            # Handle imbalanced data
            X_train_balanced, y_train_balanced = trainer.handle_imbalanced_data(
                X_train, y_train, args.resampling_strategy
            )
            
            # Train specific model
            if model_type == 'ensemble':
                results = trainer.train_ensemble_model(
                    X_train_balanced, y_train_balanced, X_val, y_val, args.hyperparameter_tuning
                )
            elif model_type == 'neural':
                results = trainer.train_neural_model(
                    X_train_balanced, y_train_balanced, X_val, y_val, args.hyperparameter_tuning
                )
            elif model_type == 'anomaly':
                results = trainer.train_anomaly_model(
                    X_train, y_train, X_val, y_val  # Use original data for anomaly detection
                )
            
            # Evaluate on test set
            test_results = trainer.evaluator.evaluate_model(
                results["model"], X_test, y_test, f"{model_type}_test"
            )
            results["test_results"] = test_results
        
        # Generate reports if requested
        if args.generate_reports:
            logger.info("Generating evaluation reports")
            
            report_dir = os.path.join(args.output_dir, "reports")
            os.makedirs(report_dir, exist_ok=True)
            
            if isinstance(results, dict) and 'ensemble' in results:
                # Multiple models trained
                for model_name, model_results in results.items():
                    if model_name == 'test_results':
                        continue
                    
                    model = model_results.get("model")
                    if model:
                        # Generate report (you would need test data here)
                        logger.info(f"Report generation for {model_name} would be implemented here")
            else:
                # Single model trained
                model = results.get("model")
                if model:
                    logger.info(f"Report generation for {model_type} would be implemented here")
        
        # Save training summary
        summary = {
            "training_timestamp": datetime.now().isoformat(),
            "data_path": args.data_path,
            "model_types": models_to_train,
            "resampling_strategy": args.resampling_strategy,
            "hyperparameter_tuning": args.hyperparameter_tuning,
            "test_size": args.test_size,
            "val_size": args.val_size,
            "output_dir": args.output_dir
        }
        
        # Add results summary
        if isinstance(results, dict) and 'test_results' in results:
            summary["test_results"] = results["test_results"]
        elif isinstance(results, dict) and 'test_results' in results:
            summary["test_results"] = {model_type: results["test_results"]}
        
        summary_path = os.path.join(args.output_dir, "training_summary.json")
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        logger.info(
            "Model training completed successfully",
            models_trained=models_to_train,
            output_dir=args.output_dir,
            summary_path=summary_path
        )
        
        # Print summary
        print("\n" + "="*50)
        print("TRAINING COMPLETED SUCCESSFULLY")
        print("="*50)
        print(f"Models trained: {', '.join(models_to_train)}")
        print(f"Output directory: {args.output_dir}")
        print(f"Training summary: {summary_path}")
        
        if isinstance(results, dict) and 'test_results' in results:
            print("\nTest Results:")
            for model_name, metrics in results["test_results"].items():
                if isinstance(metrics, dict):
                    auc_roc = metrics.get('auc_roc', 'N/A')
                    f1_score = metrics.get('f1_score', 'N/A')
                    print(f"  {model_name}: AUC-ROC={auc_roc:.4f}, F1={f1_score:.4f}")
        
        print("="*50)
        
    except Exception as e:
        logger.error("Model training failed", error=str(e))
        print(f"\nERROR: Model training failed - {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
