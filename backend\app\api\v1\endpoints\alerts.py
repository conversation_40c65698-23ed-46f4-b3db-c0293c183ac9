"""
Alert management endpoints
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db_session
from app.core.logging import get_logger
from app.schemas.alert import (
    AlertResponse,
    AlertCreate,
    AlertUpdate,
    AlertStats
)
from app.services.alert_service import AlertService
from app.api.dependencies import get_current_user, require_permission

router = APIRouter()
logger = get_logger(__name__)


@router.get("/", response_model=List[AlertResponse])
async def list_alerts(
    skip: int = Query(0, ge=0, description="Number of alerts to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of alerts to return"),
    severity: Optional[str] = Query(None, description="Filter by alert severity"),
    status: Optional[str] = Query(None, description="Filter by alert status"),
    alert_type: Optional[str] = Query(None, description="Filter by alert type"),
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(require_permission("read:alerts"))
):
    """
    List alerts with optional filtering
    
    Returns paginated list of fraud alerts. Requires 'read:alerts' permission.
    """
    try:
        alert_service = AlertService(db)
        alerts = await alert_service.list_alerts(
            skip=skip,
            limit=limit,
            severity=severity,
            status=status,
            alert_type=alert_type
        )
        
        return alerts
        
    except Exception as e:
        logger.error(
            "Failed to list alerts",
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve alerts"
        )


@router.get("/{alert_id}", response_model=AlertResponse)
async def get_alert(
    alert_id: UUID,
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(require_permission("read:alerts"))
):
    """
    Get alert by ID
    
    Returns detailed alert information including related transaction data.
    """
    try:
        alert_service = AlertService(db)
        alert = await alert_service.get_alert(alert_id)
        
        if not alert:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Alert not found"
            )
        
        return alert
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to get alert",
            alert_id=alert_id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve alert"
        )


@router.post("/", response_model=AlertResponse)
async def create_alert(
    alert_data: AlertCreate,
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(require_permission("write:alerts"))
):
    """
    Create a new alert
    
    Creates a manual fraud alert. Requires 'write:alerts' permission.
    """
    try:
        alert_service = AlertService(db)
        
        alert = await alert_service.create_alert(
            alert_data=alert_data,
            created_by=current_user.id
        )
        
        logger.info(
            "Alert created",
            alert_id=alert.id,
            transaction_id=alert_data.transaction_id,
            created_by=current_user.id
        )
        
        return alert
        
    except Exception as e:
        logger.error(
            "Failed to create alert",
            transaction_id=alert_data.transaction_id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create alert"
        )


@router.put("/{alert_id}", response_model=AlertResponse)
async def update_alert(
    alert_id: UUID,
    alert_data: AlertUpdate,
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(require_permission("write:alerts"))
):
    """
    Update alert status and information
    
    Updates alert details, typically used for investigation results. Requires 'write:alerts' permission.
    """
    try:
        alert_service = AlertService(db)
        
        # Get existing alert
        existing_alert = await alert_service.get_alert(alert_id)
        if not existing_alert:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Alert not found"
            )
        
        # Update alert
        alert = await alert_service.update_alert(
            alert_id=alert_id,
            alert_data=alert_data,
            updated_by=current_user.id
        )
        
        logger.info(
            "Alert updated",
            alert_id=alert_id,
            status=alert_data.status,
            updated_by=current_user.id
        )
        
        return alert
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to update alert",
            alert_id=alert_id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update alert"
        )


@router.post("/{alert_id}/acknowledge")
async def acknowledge_alert(
    alert_id: UUID,
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(require_permission("write:alerts"))
):
    """
    Acknowledge an alert
    
    Marks an alert as acknowledged by the current user.
    """
    try:
        alert_service = AlertService(db)
        
        alert = await alert_service.acknowledge_alert(
            alert_id=alert_id,
            acknowledged_by=current_user.id
        )
        
        if not alert:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Alert not found"
            )
        
        logger.info(
            "Alert acknowledged",
            alert_id=alert_id,
            acknowledged_by=current_user.id
        )
        
        return {"message": "Alert acknowledged successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to acknowledge alert",
            alert_id=alert_id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to acknowledge alert"
        )


@router.post("/{alert_id}/resolve")
async def resolve_alert(
    alert_id: UUID,
    resolution_notes: str = Query(..., description="Resolution notes"),
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(require_permission("write:alerts"))
):
    """
    Resolve an alert
    
    Marks an alert as resolved with resolution notes.
    """
    try:
        alert_service = AlertService(db)
        
        alert = await alert_service.resolve_alert(
            alert_id=alert_id,
            resolution_notes=resolution_notes,
            resolved_by=current_user.id
        )
        
        if not alert:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Alert not found"
            )
        
        logger.info(
            "Alert resolved",
            alert_id=alert_id,
            resolved_by=current_user.id
        )
        
        return {"message": "Alert resolved successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to resolve alert",
            alert_id=alert_id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to resolve alert"
        )


@router.get("/stats/summary", response_model=AlertStats)
async def get_alert_stats(
    days: int = Query(7, ge=1, le=365, description="Number of days to include in stats"),
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(require_permission("read:analytics"))
):
    """
    Get alert statistics
    
    Returns comprehensive alert statistics including counts by severity, status, and trends.
    """
    try:
        alert_service = AlertService(db)
        stats = await alert_service.get_alert_stats(days=days)
        
        return stats
        
    except Exception as e:
        logger.error(
            "Failed to get alert stats",
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve alert statistics"
        )
